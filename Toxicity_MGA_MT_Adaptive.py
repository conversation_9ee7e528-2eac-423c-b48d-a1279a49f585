import numpy as np
from utils import build_dataset
import torch
from torch.optim import <PERSON>
from torch.utils.data import DataLoader
from utils.MY_GNN import collate_molgraphs, EarlyStopping, set_random_seed, MGA, \
    run_an_eval_epoch_pih, run_an_eval_epoch_heterogeneous_AROC, run_an_eval_epoch_heterogeneous_RBA
from utils.adaptive_loss import AdaptiveMultiTaskBalancedLoss, run_a_train_epoch_with_adaptive_loss
from utils.loss_analysis import analyze_dataset_imbalance, visualize_loss_evolution, generate_loss_analysis_report
import os
import time
import pandas as pd

start = time.time()

# fix parameters of model
args = {}
args['device'] = "cuda" if torch.cuda.is_available() else "cpu"
args['atom_data_field'] = 'atom'
args['bond_data_field'] = 'etype'

# 分类指标
args['classification_AROC'] = 'roc_auc'
args['classification_RBA'] = 'RBA'

# 回归指标
args['regression_r2'] = 'r2'
args['regression_mse'] = 'mse'
args['regression_mae'] = 'mae'
args['regression_rmse'] = 'rmse'

# model parameter
args['num_epochs'] = 500
args['patience'] = 50
args['batch_size'] = 256
args['mode'] = 'higher'
args['in_feats'] = 40
args['rgcn_hidden_feats'] = [256, 256]
args['classifier_hidden_feats'] = 128
args['rgcn_drop_out'] = 0.3
args['drop_out'] = 0.4
args['lr'] = 3
args['weight_decay'] = 5
args['loop'] = True
args['gradient_clip_norm'] = 1.0

# 自适应损失函数参数 - 针对多任务优化
args['adaptive_loss'] = {
    'alpha': 0.3,               # 增加对少数类的关注
    'gamma': 2.5,               # 增强对难样本的聚焦
    'beta': 0.999,              # 更快的权重适应
    'temperature_init': 2.0,     # 更高的初始温度，允许更大的权重差异
    'temperature_decay': 0.99,   # 较慢的温度衰减
    'gradient_clip': 3.0         # 更宽松的梯度裁剪
}

# task name (model name) - 多任务设置
args['task_name'] = 'MT-AquaTox-adaptive'
args['data_name'] = 'AquaTox_scr'
args['times'] = 5  # 减少运行次数以便快速测试

# 多任务设置 - 包含所有5个任务
args['select_task_list'] = ['FishLC50', 'FishEL_NOEC', 'DMRepNOEC', 'DMImbEC50', 'AlaGroErC50']
args['select_task_index'] = []
args['classification_num'] = 0
args['regression_num'] = 0
args['all_task_list'] = ['FishLC50', 'FishEL_NOEC', 'DMRepNOEC', 'DMImbEC50', 'AlaGroErC50']

# generate select task index
for index, task in enumerate(args['all_task_list']):
    if task in args['select_task_list']:
        args['select_task_index'].append(index)

# generate classification_num (所有任务都是分类任务)
for task in args['select_task_list']:
    if task in ['FishLC50', 'FishEL_NOEC', 'DMRepNOEC', 'DMImbEC50', 'AlaGroErC50']:
        args['classification_num'] = args['classification_num'] + 1

args['task_class'] = 'classification'
print('Classification task:{}, Regression Task:{}'.format(args['classification_num'], args['regression_num']))

args['bin_path'] = 'data/' + args['data_name'] + '.bin'
args['group_path'] = 'data/' + args['data_name'] + '_group.csv'

# 结果存储
result_pd_AROC = pd.DataFrame(columns=args['select_task_list']+['group'] + args['select_task_list']+['group']
                         + args['select_task_list']+['group'])
result_pd_RBA = pd.DataFrame(columns=args['select_task_list']+['group'] + args['select_task_list']+['group']
                         + args['select_task_list']+['group'])

# 损失函数分析结果存储
loss_analysis_results = []

# 首先进行数据集不平衡分析
print("正在加载数据进行预分析...")
train_set_sample, _, _, _ = build_dataset.load_graph_from_csv_bin_for_splited(
    bin_path=args['bin_path'],
    group_path=args['group_path'],
    select_task_index=args['select_task_index']
)

# 分析数据集不平衡情况
dataset_analysis = analyze_dataset_imbalance(
    train_set_sample, 
    args['classification_num'], 
    args['select_task_list']
)

all_times_train_result = []
all_times_val_result = []
all_times_test_result = []

for time_id in range(args['times']):
    set_random_seed(2020+time_id)
    
    print('***************************************************************************************************')
    print('{}, {}/{} time'.format(args['task_name'], time_id+1, args['times']))
    print('***************************************************************************************************')
    
    # 加载数据
    train_set, val_set, test_set, task_number = build_dataset.load_graph_from_csv_bin_for_splited(
        bin_path=args['bin_path'],
        group_path=args['group_path'],
        select_task_index=args['select_task_index']
    )
    
    from torch.utils.data import ConcatDataset
    all_dataset = ConcatDataset([train_set, val_set, test_set])
    all_dataloader = DataLoader(dataset=all_dataset, batch_size=args['batch_size'], 
                               collate_fn=collate_molgraphs, shuffle=False)
    
    print("Molecule graph generation is complete !")
    
    train_loader = DataLoader(dataset=train_set, batch_size=args['batch_size'],
                              shuffle=True, collate_fn=collate_molgraphs)
    val_loader = DataLoader(dataset=val_set, batch_size=args['batch_size'],
                            shuffle=True, collate_fn=collate_molgraphs)
    test_loader = DataLoader(dataset=test_set, batch_size=args['batch_size'],
                             collate_fn=collate_molgraphs)
    
    # 初始化自适应损失函数
    adaptive_loss_fn = AdaptiveMultiTaskBalancedLoss(
        classification_num=args['classification_num'],
        regression_num=args['regression_num'],
        alpha=args['adaptive_loss']['alpha'],
        gamma=args['adaptive_loss']['gamma'],
        beta=args['adaptive_loss']['beta'],
        temperature_init=args['adaptive_loss']['temperature_init'],
        temperature_decay=args['adaptive_loss']['temperature_decay'],
        gradient_clip=args['adaptive_loss']['gradient_clip'],
        device=args['device']
    )
    
    # 初始化模型
    model = MGA(in_feats=args['in_feats'], rgcn_hidden_feats=args['rgcn_hidden_feats'],
                n_tasks=task_number, rgcn_drop_out=args['rgcn_drop_out'],
                classifier_hidden_feats=args['classifier_hidden_feats'], dropout=args['drop_out'],
                loop=args['loop'])
    
    optimizer = Adam(model.parameters(), lr=10**-args['lr'], weight_decay=10**-args['weight_decay'])
    stopper = EarlyStopping(patience=args['patience'], task_name=args['task_name'], mode=args['mode'])
    model.to(args['device'])
    
    # 训练过程中的损失信息记录
    training_loss_history = []
    
    print(f"开始训练，使用自适应多任务平衡损失函数...")
    print(f"任务列表: {args['select_task_list']}")
    
    for epoch in range(args['num_epochs']):
        # 使用自适应损失函数训练
        avg_loss, loss_info = run_a_train_epoch_with_adaptive_loss(
            args, epoch, model, train_loader, adaptive_loss_fn, optimizer
        )
        
        # 记录损失信息
        training_loss_history.append({
            'epoch': epoch,
            'avg_loss': avg_loss,
            'task_losses': loss_info['task_losses'].cpu().numpy(),
            'task_weights': loss_info['task_weights'].cpu().numpy(),
            'temperature': adaptive_loss_fn.temperature.item()
        })
        
        # 验证和早停
        validation_result = run_an_eval_epoch_heterogeneous_AROC(args, model, val_loader)
        val_score = np.mean(validation_result)
        early_stop = stopper.step(val_score, model)
        
        print('epoch {:d}/{:d}, validation {:.4f}, best validation {:.4f}'.format(
            epoch + 1, args['num_epochs'], val_score, stopper.best_score) + 
            ' validation result:', validation_result)
        
        if early_stop:
            break
    
    # 加载最佳模型
    stopper.load_checkpoint(model)
    
    # 评估
    train_AROC = run_an_eval_epoch_heterogeneous_AROC(args, model, train_loader)
    val_AROC = run_an_eval_epoch_heterogeneous_AROC(args, model, val_loader)
    test_AROC = run_an_eval_epoch_heterogeneous_AROC(args, model, test_loader)
    
    train_RBA = run_an_eval_epoch_heterogeneous_RBA(args, model, train_loader)
    val_RBA = run_an_eval_epoch_heterogeneous_RBA(args, model, val_loader)
    test_RBA = run_an_eval_epoch_heterogeneous_RBA(args, model, test_loader)
    
    # 保存结果
    result_AROC = train_AROC + ['train'] + val_AROC + ['valid'] + test_AROC + ['test']
    result_pd_AROC.loc[time_id] = result_AROC
    result_RBA = train_RBA + ['train'] + val_RBA + ['valid'] + test_RBA + ['test']
    result_pd_RBA.loc[time_id] = result_RBA
    
    # 保存损失分析结果
    loss_analysis_results.append({
        'time_id': time_id,
        'training_history': training_loss_history,
        'final_task_stats': adaptive_loss_fn.get_task_statistics()
    })
    
    print('********************************{}, {}_times_result*******************************'.format(
        args['task_name'], time_id+1))
    print("train_result:", train_AROC, train_RBA)
    print("val_result:", val_AROC, val_RBA)
    print("test_result:", test_AROC, test_RBA)
    
    # 打印任务权重信息
    final_stats = adaptive_loss_fn.get_task_statistics()
    print("最终任务权重分布:")
    for i, (task_name, weight) in enumerate(zip(args['select_task_list'], final_stats['task_weights'])):
        print(f"  {task_name}: {weight.item():.4f}")
    
    # 生成预测结果
    run_an_eval_epoch_pih(args, model, all_dataloader, 
                         output_path='jieguo/'+args['task_name']+'prediction.csv')

# 保存结果
result_pd_AROC.to_csv('result/'+args['task_name']+'_'.join(args['select_task_list'])+'_result_AROC.csv', index=None)
result_pd_RBA.to_csv('result/'+args['task_name']+'_'.join(args['select_task_list'])+'_result_RBA.csv', index=None)

# 生成损失函数分析
print("\n正在生成损失函数分析...")
visualize_loss_evolution(loss_analysis_results, save_dir='analysis/adaptive_loss')
generate_loss_analysis_report(loss_analysis_results, dataset_analysis, 
                             save_path='analysis/adaptive_loss_report.txt')

elapsed = (time.time() - start)
m, s = divmod(elapsed, 60)
h, m = divmod(m, 60)
print("Time used:", "{:d}:{:d}:{:d}".format(int(h), int(m), int(s)))

print("\n" + "="*60)
print("自适应多任务平衡损失函数实验完成！")
print("="*60)
print("创新点总结:")
print("1. 🎯 Focal Loss机制：自动关注难分类样本和少数类")
print("2. ⚖️  动态任务权重：基于数据量和学习难度自适应调整")
print("3. 🌡️  温度调节：训练过程中逐渐精细化权重分配")
print("4. 📊 梯度平衡：确保各任务梯度贡献相对均衡")
print("5. 📈 实时监控：提供详细的训练过程分析")
print("\n分析结果已保存到 analysis/ 目录")
print("="*60)
