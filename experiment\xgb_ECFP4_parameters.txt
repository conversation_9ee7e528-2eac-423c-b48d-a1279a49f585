FishLC50:{'max_depth': 9, 'min_child_weight': 1, 'gamma': 0.12, 'reg_lambda': 0.01, 'reg_alpha': 0.01, 'lr': 0.05, 'n_estimators': 160, 'colsample_bytree': 0.8, 'subsample': 0.85}
FishEL_NOEC:{'max_depth': 9, 'min_child_weight': 4, 'gamma': 0.06, 'reg_lambda': 1e-05, 'reg_alpha': 1e-05, 'lr': 0.05, 'n_estimators': 220, 'colsample_bytree': 0.75, 'subsample': 0.85}
DMRepNOEC:{'max_depth': 5, 'min_child_weight': 2, 'gamma': 0.1, 'reg_lambda': 1, 'reg_alpha': 1e-05, 'lr': 0.05, 'n_estimators': 240, 'colsample_bytree': 0.85, 'subsample': 0.75}
DMImbEC50:{'max_depth': 4, 'min_child_weight': 4, 'gamma': 0.02, 'reg_lambda': 1, 'reg_alpha': 1, 'lr': 0.05, 'n_estimators': 220, 'colsample_bytree': 0.85, 'subsample': 0.8}
AlaGroErC50:{'max_depth': 7, 'min_child_weight': 1, 'gamma': 0.06, 'reg_lambda': 0.1, 'reg_alpha': 1e-05, 'lr': 0.05, 'n_estimators': 240, 'colsample_bytree': 0.8, 'subsample': 0.8}
