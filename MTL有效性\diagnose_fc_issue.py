#!/usr/bin/env python3
"""
诊断FC参数问题的脚本
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

def simulate_zero_vector_problem():
    """模拟零向量问题"""
    print("=" * 60)
    print("模拟零向量导致的相似性问题")
    print("=" * 60)
    
    # 模拟当前可能的情况：
    # 某些模型有FC参数，某些模型没有FC参数（被设为零向量）
    
    print("假设场景：某些模型的FC参数提取失败，被设为零向量")
    
    # FishAT: 有真实的FC参数
    fish_at_params = np.random.normal(0.1, 0.05, 128)
    
    # FishCT: FC参数提取失败，变成零向量
    fish_ct_params = np.array([0.0])
    
    # DMCT: FC参数提取失败，变成零向量  
    dm_ct_params = np.array([0.0])
    
    # DMAT: FC参数提取失败，变成零向量
    dm_at_params = np.array([0.0])
    
    # AlgAT: 有真实的FC参数
    alg_at_params = np.random.normal(-0.1, 0.08, 256)
    
    print(f"FishAT: {len(fish_at_params)}维, 均值={np.mean(fish_at_params):.4f}")
    print(f"FishCT: {len(fish_ct_params)}维, 均值={np.mean(fish_ct_params):.4f} (零向量)")
    print(f"DMCT: {len(dm_ct_params)}维, 均值={np.mean(dm_ct_params):.4f} (零向量)")
    print(f"DMAT: {len(dm_at_params)}维, 均值={np.mean(dm_at_params):.4f} (零向量)")
    print(f"AlgAT: {len(alg_at_params)}维, 均值={np.mean(alg_at_params):.4f}")
    
    # 模拟零填充处理
    print(f"\n应用零填充处理:")
    max_dim = max(len(fish_at_params), len(fish_ct_params), len(dm_ct_params), 
                  len(dm_at_params), len(alg_at_params))
    print(f"最大维度: {max_dim}")
    
    # 零填充到最大维度
    fish_at_padded = np.concatenate([fish_at_params, np.zeros(max_dim - len(fish_at_params))])
    fish_ct_padded = np.concatenate([fish_ct_params, np.zeros(max_dim - len(fish_ct_params))])
    dm_ct_padded = np.concatenate([dm_ct_params, np.zeros(max_dim - len(dm_ct_params))])
    dm_at_padded = np.concatenate([dm_at_params, np.zeros(max_dim - len(dm_at_params))])
    alg_at_padded = np.concatenate([alg_at_params, np.zeros(max_dim - len(alg_at_params))])
    
    print(f"填充后所有向量都是{max_dim}维")
    
    # 计算相似性
    params_matrix = np.array([fish_at_padded, fish_ct_padded, dm_ct_padded, dm_at_padded, alg_at_padded])
    similarity_matrix = cosine_similarity(params_matrix)
    
    labels = ['FishAT', 'FishCT', 'DMCT', 'DMAT', 'AlgAT']
    
    print(f"\n零向量问题导致的相似性矩阵:")
    print("        ", "  ".join(f"{label:>8}" for label in labels))
    for i, label in enumerate(labels):
        row = "  ".join(f"{similarity_matrix[i][j]:8.4f}" for j in range(len(labels)))
        print(f"{label:>8}: {row}")
    
    # 分析结果
    print(f"\n结果分析:")
    print(f"FishAT vs FishCT: {similarity_matrix[0][1]:.4f}")
    print(f"FishCT vs DMCT: {similarity_matrix[1][2]:.4f}")
    print(f"FishCT vs DMAT: {similarity_matrix[1][3]:.4f}")
    print(f"FishAT vs AlgAT: {similarity_matrix[0][4]:.4f}")
    
    # 检查是否与观察到的模式一致
    if (similarity_matrix[0][1] < 0.2 and  # FishAT vs FishCT 很低
        similarity_matrix[1][2] > 0.9 and  # FishCT vs DMCT 很高
        similarity_matrix[0][4] > 0.8):    # FishAT vs AlgAT 很高
        print("\n✅ 这与观察到的问题模式完全一致！")
        print("   问题原因：某些模型的FC参数提取失败，被设为零向量")
        print("   零向量之间相似性很高，零向量与非零向量相似性很低")
    
    return similarity_matrix

def suggest_fix():
    """建议修复方案"""
    print(f"\n" + "=" * 60)
    print("问题诊断和修复建议")
    print("=" * 60)
    
    print("问题诊断:")
    print("1. 某些单任务模型的FC参数提取失败")
    print("2. 失败的模型被设为零向量 np.array([0.0])")
    print("3. 零向量之间相似性很高(接近1.0)")
    print("4. 零向量与真实参数相似性很低(接近0.1)")
    
    print(f"\n修复建议:")
    print("1. 检查单任务模型的FC层结构")
    print("2. 改进FC参数提取逻辑")
    print("3. 对于没有FC参数的模型，应该排除而不是设为零向量")
    print("4. 或者使用其他参数（如GNN参数）进行比较")
    
    print(f"\n立即行动:")
    print("1. 运行带调试信息的任务B")
    print("2. 查看哪些模型的FC参数为空")
    print("3. 根据调试信息调整提取逻辑")

def main():
    """主函数"""
    print("诊断FC参数相似性计算问题...")
    
    simulate_zero_vector_problem()
    suggest_fix()
    
    print(f"\n" + "=" * 60)
    print("结论：问题很可能是FC参数提取失败导致的零向量问题")
    print("需要查看详细的调试信息来确认")
    print("=" * 60)

if __name__ == "__main__":
    main()
