FishLC50:{'max_depth': 18, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_features': 'log2', 'n_estimators': 200, 'bootstrap': True, 'max_samples': 1.0}
FishEL_NOEC:{'max_depth': 17, 'min_samples_split': 5, 'min_samples_leaf': 1, 'max_features': None, 'n_estimators': 350, 'bootstrap': False, 'max_samples': 0.8}
DMRepNOEC:{'max_depth': 11, 'min_samples_split': 7, 'min_samples_leaf': 2, 'max_features': None, 'n_estimators': 250, 'bootstrap': True, 'max_samples': 0.9}
DMImbEC50:{'max_depth': 19, 'min_samples_split': 6, 'min_samples_leaf': 1, 'max_features': 'log2', 'n_estimators': 250, 'bootstrap': False, 'max_samples': 0.9}
AlaGroErC50:{'max_depth': 11, 'min_samples_split': 4, 'min_samples_leaf': 3, 'max_features': None, 'n_estimators': 250, 'bootstrap': True, 'max_samples': 0.9}
