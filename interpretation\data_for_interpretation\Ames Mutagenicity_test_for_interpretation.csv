smiles,Ames Mutagenicity,group
O=[N+]([O-])c1cc(C(O)=Nc2cccc(Br)c2)cs1,1,test
O=[N+]([O-])c1ccc(Oc2ccc(Cl)cc2Cl)cc1,1,test
O=[N+]([O-])c1ccc([N+](=O)[O-])c(O)c1,1,test
O=[N+]([O-])c1ccc2cccc3c2c1CC3,1,test
Nc1ccc(C=Cc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=C(Cl)c1ccccc1[N+](=O)[O-],1,test
Nc1ccc(Oc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1ccc(-c2ccc([N+](=O)[O-])cc2[N+](=O)[O-])cc1,1,test
COc1ccccc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc2ccc3c(O)ccc4c5ccccc5c1c2c34,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c(Cl)c([N+](=O)[O-])c1,1,test
O=c1oc2c([N+](=O)[O-])cccc2c2ccccc12,1,test
Cc1ccc(S(=O)(=O)OCc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=C(O)c1ccc(Cl)c([N+](=O)[O-])c1,1,test
[O-][N+]([O-])=C1CCCC1,1,test
COc1ccc(N(CCCl)CCCl)c2cc([N+](=O)[O-])oc12,1,test
O=[N+]([O-])c1cc2c3c(c1)CCc1cc([N+](=O)[O-])cc(c1-3)CC2,1,test
O=[N+]([O-])c1ccc2nc3ccccc3nc2c1,1,test
CCOP(=O)(Oc1ccc([N+](=O)[O-])cc1)c1ccccc1,1,test
O=c1c(=O)c2ccc([N+](=O)[O-])c3ccc4cccc1c4c32,1,test
Nc1cccc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cccc2c(Cl)nsc12,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c2cccc3c2c1-c1ccccc1-3,1,test
O=[N+]([O-])c1ccccc1SSC(Cl)=C(Cl)Cl,1,test
CN(C)CCCN=c1c2ccccc2[nH]c2c(Cl)ccc([N+](=O)[O-])c12,1,test
O=[N+]([O-])c1ccc2c(c1)CCN2,1,test
O=[N+]([O-])c1cc(C(O)=Nc2ccccc2F)cs1,1,test
Cn1c([N+](=O)[O-])cnc1C1NC(CO)(CO)CO1,1,test
Cc1ccc([N+](=O)[O-])c([N+](=O)[O-])c1,1,test
O=C1N=C(O)CN1N=CC=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1cccc2cccc([N+](=O)[O-])c12,1,test
COC(=O)C(=Cc1ccc([N+](=O)[O-])o1)[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc(Cl)c([N+](=O)[O-])c1,1,test
COC(=O)C(C#N)=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2c3c4c(cccc4c4ccccc4c13)C(O)C2O,1,test
O=C(O)C=Cc1cccc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc(Nc2ccccc2O)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2c(c1)c([N+](=O)[O-])cc1ccccc12,1,test
O=[N+]([O-])c1cc2cccc3c2c(c1[N+](=O)[O-])-c1ccccc1-3,1,test
C=Cc1ccc([N+](=O)[O-])cc1,1,test
N=C(O)C(=Cc1ccc([N+](=O)[O-])o1)c1ccco1,1,test
O=[N+]([O-])c1cc(C(O)=Nc2ccccc2Br)cs1,1,test
N#Cc1ccc(C=Cc2ccc([N+](=O)[O-])cc2)cc1,1,test
[N-]=[N+]=Nc1ccc(F)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc(I)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cc(CCl)cc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cnc(CO)n1CCO,1,test
CC(O)=Nc1ccc(C=CC(=O)c2ccccc2)cc1,1,test
O=[N+]([O-])c1cccc2c[nH]nc12,1,test
O=[N+]([O-])c1ccc2c(c1)CCc1cc3c(cc1-2)CCCC3,1,test
O=[N+]([O-])c1cccc2cccnc12,1,test
O=[N+]([O-])c1cc2c(ccc3cc(N(CCCl)CCCl)ccc32)o1,1,test
O=[N+]([O-])c1ccc(C=Cc2ccc(Cl)cc2)cc1,1,test
Nc1ccc(Nc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1ccc(C=NN=C(O)c2ccc(O)cc2)o1,1,test
Nc1ccc(N)c2c1C(=O)c1cccc([N+](=O)[O-])c1C2=O,1,test
O=[N+]([O-])c1ccc(-c2ccccc2[N+](=O)[O-])c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2ccc3ccc(O)c4c5ccccc5c1c2c34,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c(O)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2ccc3c([N+](=O)[O-])ccc4ccc1c2c43,1,test
O=[N+]([O-])c1ccc2ccc3c4c(cc5ccc1c2c53)C(O)C(O)C=C4,1,test
Nc1c(Cl)cc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc(N=Nc2ccc(N(CCO)CCO)cc2)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccccc1[N+](=O)[O-],1,test
CC(=O)c1oc([N+](=O)[O-])c(-c2ccccc2)c1-c1ccccc1,1,test
O=[N+]([O-])c1ccc(Br)c([N+](=O)[O-])c1,1,test
Nc1cc([N+](=O)[O-])c(N)cc1Cl,1,test
O=[N+]([O-])c1cc2c3c(ccc4cccc(c43)CC2)c1,1,test
O=Nc1ccc2ccc3ccc([N+](=O)[O-])c4ccc1c2c34,1,test
O=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccccc1SSC(F)=C(Cl)Cl,1,test
COC(O)=Nc1nc2ccc(C(=NO)c3ccc(F)cc3)cc2[nH]1,1,test
Nc1cc([N+](=O)[O-])ccc1C(=O)O,1,test
CN(C)CCCN=c1c2ccccc2[nH]c2c(F)ccc([N+](=O)[O-])c12,1,test
O=CN(O)c1ccc([N+](=O)[O-])cc1,1,test
CC(C)NCC1CCc2cc(CO)c([N+](=O)[O-])cc2N1,1,test
O=C(Nc1ccc(Cl)cc1)c1csc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c2c([N+](=O)[O-])cc([N+](=O)[O-])cc2c1,1,test
CC(=O)Oc1cc([N+](=O)[O-])c2ccc3cccc4ccc1c2c43,1,test
O=Cc1c([N+](=O)[O-])cccc1[N+](=O)[O-],1,test
Cc1ccc2c([N+](=O)[O-])cccc2c1,1,test
O=[N+]([O-])c1cccc(O)c1[N+](=O)[O-],1,test
CN1C(=O)CN=C(c2ccccc2F)c2cc([N+](=O)[O-])ccc21,1,test
O=C1OC(=O)c2cc([N+](=O)[O-])ccc21,1,test
O=[N+]([O-])c1ccc(C=CC(O)=NN=Cc2ccc([N+](=O)[O-])o2)o1,1,test
O=[N+]([O-])c1ccc2snc(Cl)c2c1,1,test
O=[N+]([O-])c1cccc(CCl)c1,1,test
O=[N+]([O-])c1ccc2c(CCO)c[nH]c2c1,1,test
O=[N+]([O-])c1ccc2ccc3cccc([N+](=O)[O-])c3c2c1,1,test
O=[N+]([O-])c1ccc(CCl)cc1,1,test
Cc1c(C(O)=NCCO)[n+]([O-])c2ccccc2[n+]1[O-],1,test
CN(C)CCCN=c1c2ccccc2[nH]c2ccc([N+](=O)[O-])cc12,1,test
O=C(Nc1ccc([N+](=O)[O-])cc1)c1csc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cc2ccc3cc4ccccc4c4ccc(c1)c2c34,1,test
Cc1cc([N+](=O)[O-])ccc1N,1,test
O=[N+]([O-])c1cc2cc(N(CCCl)CCCl)ccc2o1,1,test
CN(C)N=Nc1ccc([N+](=O)[O-])cc1,1,test
CCc1ccc2c(c1[N+](=O)[O-])C(=O)c1ccccc1C2=O,1,test
O=[N+]([O-])c1ccc2ccc3c4c(cc5ccc1c2c53)C=CC(O)C4O,1,test
O=[N+]([O-])c1ccc(C=C=[N+]([O-])O)o1,1,test
Cc1c([N+](=O)[O-])cc([N+](=O)[O-])c(N)c1[N+](=O)[O-],1,test
O=[N+]([O-])c1cc2cccc3c4ccccc4c4cccc1c4c23,1,test
O=[N+]([O-])c1cc2c3c(c1)CCc1c4c(cc(c1-3)CC2)CCCC4,1,test
O=[N+]([O-])c1ccc2[nH]ncc2c1,1,test
O=NN(c1ccc([N+](=O)[O-])cc1)C1OC(CO)C(O)C1O,1,test
O=[N+]([O-])c1ccc2ccc3ccc([N+](=O)[O-])c4ccc1c2c34,1,test
O=[N+]([O-])c1ccc2c3cccc4ccc([N+](=O)[O-])c(c5cccc1c25)c43,1,test
COP(=S)(OC)Oc1ccc([N+](=O)[O-])cc1,1,test
CN(c1c([N+](=O)[O-])cc([N+](=O)[O-])cc1[N+](=O)[O-])[N+](=O)[O-],1,test
O=[N+]([O-])c1cc2ccc3cc([N+](=O)[O-])cc4ccc(c1)c2c34,1,test
Cc1c([N+](=O)[O-])c2ccccc2c2ccc3ccccc3c12,1,test
O=[N+]([O-])c1c(-c2ccc(Cl)cc2)nc2sccn12,1,test
O=[N+]([O-])c1ccccc1SSC(Cl)=C(Cl)C(F)(F)F,1,test
COc1ccc(C(O)=Nc2ccccc2)cc1[N+](=O)[O-],1,test
CN(C)CCCN=c1c2ccccc2[nH]c2c(N(C)C)ccc([N+](=O)[O-])c12,1,test
O=[N+]([O-])c1cc2ccccc2c2ccccc12,1,test
O=[N+]([O-])c1ccc2ccc3c([N+](=O)[O-])cccc3c2c1,1,test
CCC(Cl)=[N+]([O-])O,1,test
O=[N+]([O-])c1cc(C(O)=Nc2cccc(Cl)c2)cs1,1,test
O=[N+]([O-])c1c(O)ccc2ccccc12,1,test
O=[N+]([O-])c1ccc2cc3c(cc2c1)C=CC(O)C3O,1,test
Nc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
CC(=O)OCc1ccc([N+](=O)[O-])cc1,1,test
O=C1c2ccccc2-c2ccc([N+](=O)[O-])cc21,1,test
O=[N+]([O-])c1ccc(OCC2CO2)cc1,1,test
N#Cc1cccc([N+](=O)[O-])c1,1,test
COP(=O)(OC)Oc1ccc([N+](=O)[O-])c(C)c1,1,test
O=[N+]([O-])c1ccc2c([N+](=O)[O-])cc3ccccc3c2c1,1,test
[O-][N+](O)=C=Cc1ccccc1,1,test
COc1nsc2cc([N+](=O)[O-])ccc12,1,test
O=[N+]([O-])c1ccc2c(Cl)nsc2c1,1,test
O=[N+]([O-])c1cccc(N=C(O)c2csc([N+](=O)[O-])c2)c1,1,test
O=c1c2[nH]c3ccccc3c2nnn1N=Cc1ccc([N+](=O)[O-])cc1,1,test
O=C(O)c1cc2c(c3c1c([N+](=O)[O-])cc1c(O)cccc13)OCO2,1,test
C=COc1ccc([N+](=O)[O-])cc1,1,test
N#Cc1cccc(C=Cc2ccc([N+](=O)[O-])cc2)c1,1,test
O=[N+]([O-])c1cc2c3c(c1)CCc1cccc(c1-3)CC2,1,test
O=[N+]([O-])c1cc2ccc(N(CCCl)CCCl)cc2o1,1,test
O=[N+]([O-])c1ccc2c3c(cccc13)-c1cc3ccccc3cc1-2,1,test
Cn1c2ccccc2c2cc([N+](=O)[O-])ccc21,1,test
O=[N+]([O-])c1ccc2ccc3ccc([N+](=O)[O-])cc3c2c1,1,test
CC(=O)Oc1c(OC(C)=O)c2c([N+](=O)[O-])ccc3c4ccccc4c4cccc1c4c23,1,test
[O-][N+](O)=CCl,1,test
O=[N+]([O-])c1ccc2c(c1)CCc1c-2ccc2c1CCCC2,1,test
CCC(=O)OCc1ccc([N+](=O)[O-])cc1,1,test
Cc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc(CO)c([N+](=O)[O-])c1,1,test
O=NN(c1ccc([N+](=O)[O-])cc1)C1OCC(O)C(O)C1O,1,test
N=C(O)c1csc([N+](=O)[O-])c1,1,test
O=C1c2c(O)ccc([N+](=O)[O-])c2C(=O)c2c([N+](=O)[O-])ccc(O)c21,1,test
O=[N+]([O-])c1ccc(N=C(O)c2cc(Cl)ccc2O)c(Cl)c1,1,test
CC(C)(C)OC(O)=NN=Cc1c[n+]([O-])c2ccccc2[n+]1[O-],1,test
O=C(O)Cc1ccccc1[N+](=O)[O-],1,test
O=C(O)CC=[N+]([O-])O,1,test
Cc1cn2c([N+](=O)[O-])c(-c3ccc([N+](=O)[O-])cc3)nc2s1,1,test
O=CC=Cc1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc2ccc3c4ccccc4cc4ccc1c2c43,1,test
O=[N+]([O-])c1cccc(-c2ccc(O)c([N+](=O)[O-])c2)c1,1,test
CC(C)CN=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc(C=Cc2ccccc2)cc1,1,test
O=[N+]([O-])c1cccc2cnsc12,1,test
N#[N+]c1ccc([N+](=O)[O-])cc1,1,test
Cc1ccc([N+](=O)[O-])c(C)c1[N+](=O)[O-],1,test
O=Cc1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc2oc3ccccc3c2c1,1,test
O=[N+]([O-])c1cc2c3ccccc3ccc2c2ccccc12,1,test
N#CSc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
NNc1ccc([N+](=O)[O-])cc1,1,test
CN(C)CCCN=c1c2ccccc2[nH]c2cc([N+](=O)[O-])ccc12,1,test
O=[N+]([O-])c1ccc(O)cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1cc(C(O)=Nc2ccccc2[N+](=O)[O-])cs1,1,test
O=[N+]([O-])c1ccc(Cl)c(Cl)c1,1,test
Cn1c([N+](=O)[O-])nc2ncc(-c3ccccc3)cc21,1,test
COc1ccc(N(CCCl)CCCl)c2oc([N+](=O)[O-])cc12,1,test
CCCCN(CCO[N+](=O)[O-])[N+](=O)[O-],1,test
O=C(O)c1ccccc1[N+](=O)[O-],1,test
O=[N+]([O-])c1cc2cccc3c4c(c5cccc1c5c23)CCCC4,1,test
CC(C)OC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2ccc3ccc(O)c4ccc1c2c34,1,test
O=[N+]([O-])c1ccc(Nc2ccc(O)cc2)c([N+](=O)[O-])c1,1,test
Cc1ccc2ccccc2c1[N+](=O)[O-],1,test
O=CC=Cc1ccccc1[N+](=O)[O-],1,test
O=C(O)c1ccc([N+](=O)[O-])cc1,1,test
Cc1cc(N)cc([N+](=O)[O-])c1N,1,test
COc1cccc(C=Cc2ccc([N+](=O)[O-])cc2)c1,1,test
O=[N+]([O-])c1ccc2c(c1)c([N+](=O)[O-])cc1cccc([N+](=O)[O-])c12,1,test
O=[N+]([O-])c1cccc(-c2ccc([N+](=O)[O-])cc2[N+](=O)[O-])c1,1,test
Nc1ccc(Cc2ccc(N)cc2[N+](=O)[O-])c([N+](=O)[O-])c1,1,test
Nc1ccc2c(c1)Cc1cc([N+](=O)[O-])ccc1-2,1,test
O=[N+]([O-])C1=Cc2c3ccc4ccccc4c3cc3cccc1c23,1,test
Nc1ccc2[nH]c3ccccc3c2c1[N+](=O)[O-],1,test
Cc1cn2c(N=O)c(-c3ccc([N+](=O)[O-])cc3)nc2s1,1,test
O=[N+]([O-])C(Br)(Br)Br,1,test
O=[N+]([O-])c1ccc(-c2nc(N(CCO)CCO)c3ccccc3n2)s1,1,test
O=[N+]([O-])c1ccc2sncc2c1,1,test
O=[N+]([O-])c1ccc2c(ccc3ccc([N+](=O)[O-])cc32)c1,1,test
O=[N+]([O-])c1ccc2ccc3cc4ccccc4c4ccc1c2c34,1,test
O=[N+]([O-])c1ccc2c(c1)[nH]c1ccccc12,1,test
Nc1c(Cl)cc([N+](=O)[O-])cc1Cl,1,test
O=[N+]([O-])c1ccc2ccc3c4c(cc5ccc1c2c53)C=CCC4,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c2c3c(c4cccc5ccc1c2c54)CCCC3,1,test
O=C(O)Cc1ccc([N+](=O)[O-])cc1,1,test
Cc1c([N+](=O)[O-])ccc2ccccc12,1,test
N=C(O)c1cc(N2CC2)c([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc2c(c1)Oc1ccc([N+](=O)[O-])cc1O2,1,test
O=[N+]([O-])c1cccc(S(=O)(=O)OCC2CO2)c1,1,test
O=C1N=C(O)CN1N=Cc1ccc([N+](=O)[O-])o1,1,test
Cc1c([N+](=O)[O-])cc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])OCC(CO[N+](=O)[O-])O[N+](=O)[O-],1,test
Cc1ccc(C=Cc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c(Nc2c([N+](=O)[O-])cc([N+](=O)[O-])cc2[N+](=O)[O-])c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2[nH]c3ccc([N+](=O)[O-])cc3c2c1,1,test
O=[N+]([O-])c1cc2cccc3ccc4cccc1c4c32,1,test
COc1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc2nc3c([N+](=O)[O-])cccc3nc2c1,1,test
O=C(O)c1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc2c3c4c(cccc4c4ccccc4c13)C1OC21,1,test
Nc1c(Br)cc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
[O-][N+](O)=C(Cl)Cl,1,test
Cc1ccc([N+](=O)[O-])cc1N,1,test
COc1ccc2oc([N+](=O)[O-])cc2c1N(CCCl)CCCl,1,test
O=[N+]([O-])c1c2c(cc3ccccc13)C(O)C(O)C=C2,1,test
O=C1OCCN1N=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2c3c1ccc1cccc(c13)C1OC21,1,test
O=[N+]([O-])c1ccc2nc3cc([N+](=O)[O-])ccc3nc2c1,1,test
Nc1ncnc2c1ncn2-c1ccc([N+](=O)[O-])cc1,1,test
Cn1c([N+](=O)[O-])nc2c3cccnc3ccc21,1,test
O=[N+]([O-])c1ccc(C=Cc2cccc([N+](=O)[O-])c2)cc1,1,test
O=[N+]([O-])c1ccc(Br)cc1,1,test
Cn1ccsc1=[N+]=Nc1c(-c2ccccc2)n(C)c2ccccc12,1,test
Cc1cc(N)c(C)c([N+](=O)[O-])c1N,1,test
O=[N+]([O-])c1ccc2c(c1)cc([N+](=O)[O-])c1ccc([N+](=O)[O-])cc12,1,test
O=[N+]([O-])c1ccc2c(c1)Cc1cc([N+](=O)[O-])ccc1-2,1,test
Cc1c([N+](=O)[O-])cc(N)cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc2nc3ccc([N+](=O)[O-])cc3nc2c1,1,test
O=Cc1cccc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2c(c1)-c1cc3ccccc3c3cccc-2c13,1,test
O=C1c2cc(S(=O)(=O)O)ccc2C(=O)c2c1cccc2[N+](=O)[O-],1,test
O=[N+]([O-])c1cccc2c1-c1cccc3c([N+](=O)[O-])ccc-2c13,1,test
CCOC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
COCC(O)Cn1cc([N+](=O)[O-])nc1Cl,1,test
O=C(OCc1ccc([N+](=O)[O-])cc1)c1ccccc1,1,test
O=c1c(=O)c2c([N+](=O)[O-])ccc3ccc4cccc1c4c32,1,test
Cn1cc2ccc([N+](=O)[O-])cc2n1,1,test
O=[N+]([O-])c1ccc(C=NN2CCN=C2O)o1,1,test
O=[N+]([O-])c1ccc(NO)cc1,1,test
COc1cc(N)c([N+](=O)[O-])cc1N,1,test
CC(O)=Nc1ccc(Nc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1ccc(Oc2ccc(N=C=S)cc2)cc1,1,test
O=[N+]([O-])c1ccc2c3c(c4c([N+](=O)[O-])ccc5ccc1c2c54)CCCC3,1,test
Nc1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,1,test
O=[N+]([O-])c1ccc2c(c1)Oc1cc([N+](=O)[O-])ccc1O2,1,test
O=C(O)c1oc([N+](=O)[O-])c(-c2ccccc2)c1-c1ccccc1,1,test
Nc1[nH]c(-c2ccc([N+](=O)[O-])cc2)nc2ncnc1-2,1,test
O=[N+]([O-])c1c2ccccc2cc2ccccc12,1,test
COc1c([N+](=O)[O-])cc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1cccc2c1-c1cccc3cccc-2c13,1,test
O=Cc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1cccc2c1cc([N+](=O)[O-])c1cccc([N+](=O)[O-])c12,1,test
CN(C(=O)c1csc([N+](=O)[O-])c1)c1ccccc1,1,test
Nc1ccc([N+](=O)[O-])cc1N,1,test
Cc1ccc([N+](=O)[O-])cc1N=O,1,test
COc1ccc(C=Cc2ccc([N+](=O)[O-])cc2)cc1,1,test
Cc1cc([N+](=O)[O-])c(C)c2c1[nH]c1ccccc12,1,test
O=C(O)c1ccc([N+](=O)[O-])c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2ccc3cccc4c3c2c1C1OC41,1,test
O=[N+]([O-])c1ccc(-c2ccccc2)c([N+](=O)[O-])c1,1,test
CCCCCCN(N=O)C(=N)N[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc(-c2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])C1=Cc2cccc3cccc1c23,1,test
O=[N+]([O-])C1=Cc2c3c1cccc3cc1ccc3ccccc3c21,1,test
N=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2oc3ccc([N+](=O)[O-])cc3c2c1,1,test
Nc1nc(-c2ccc([N+](=O)[O-])cc2)nc2c1ncn2C1CC(O)C(CO)O1,1,test
Nc1ccc(Sc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1ccc(-c2nc3n(c2[N+](=O)[O-])CCS3)cc1,1,test
O=C(O)c1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2cccc3c2c1C=C3,1,test
O=c1oc2ccc([N+](=O)[O-])c3ccc4cccc1c4c23,1,test
O=[N+]([O-])c1ccc2c(c1)NCC2,1,test
O=[N+]([O-])c1nc2ccccc2[nH]1,1,test
COC(=O)c1cc2c(c3c1c([N+](=O)[O-])cc1c(OC)cccc13)OCO2,1,test
Cc1ccc([N+](=O)[O-])c2c(=NCCCN(C)C)c3ccccc3[nH]c12,1,test
O=[N+]([O-])c1cc2c(ccc3ccccc32)c2c1C=CC(O)C2O,1,test
O=[N+]([O-])c1ccc(Cl)cc1Cl,1,test
O=[N+]([O-])c1cc(Cl)ccc1Cl,1,test
Cc1c(N)cc([N+](=O)[O-])cc1N,1,test
O=[N+]([O-])c1ccc2ccc3cc4c(c5ccc1c2c35)C=CCC4,1,test
O=[N+]([O-])c1ccc2ccc3ccc([N+](=O)[O-])c4c5c(c1c2c34)CCCC5,1,test
O=[N+]([O-])c1ccc2c(c1)oc1ccc([N+](=O)[O-])cc12,1,test
CCN(CCO[N+](=O)[O-])[N+](=O)[O-],1,test
O=C(O)c1cc2c(c3c1c([N+](=O)[O-])cc1ccccc13)OCO2,1,test
O=[N+]([O-])c1ccc(C=Nn2cccn2)o1,1,test
O=C1c2ccccc2-c2cc([N+](=O)[O-])ccc21,1,test
CCCOC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2c(c1)-c1cccc3cccc-2c13,1,test
Nc1cc([N+](=O)[O-])cc(Cl)c1O,1,test
N#CC(C#N)=Cc1ccc([N+](=O)[O-])o1,1,test
CCCCN=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
CC(=O)c1ccc([N+](=O)[O-])cc1,1,test
Cc1cccc(N=C(O)c2csc([N+](=O)[O-])c2)c1,1,test
Nc1cc([N+](=O)[O-])ccc1O,1,test
O=C1c2cccc(S(=O)(=O)O)c2C(=O)c2cccc([N+](=O)[O-])c21,1,test
O=C(O)c1c[n+]([O-])c2ccccc2[n+]1[O-],1,test
O=[N+]([O-])c1ccc2ccccc2c1,1,test
O=[N+]([O-])c1cccc2c1ccc1ccccc12,1,test
Cc1ccc2c(c1[N+](=O)[O-])C(=O)c1ccccc1C2=O,1,test
Cc1cccc([N+](=O)[O-])c1[N+](=O)[O-],1,test
Nc1ccc(-c2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc(C=Cc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1ccc2c(O)nsc2c1,1,test
O=[N+]([O-])C1=Cc2cc3cc4ccccc4cc3c3cccc1c23,1,test
CC(=O)c1cccn1[N+](=O)[O-],1,test
Nc1ccc(-c2ccc([N+](=O)[O-])cc2N)c([N+](=O)[O-])c1,1,test
O=C(O)C=Cc1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1[nH]cnc1-c1ccccc1,1,test
O=[N+]([O-])c1c(-c2ccc(Cl)cc2)nc2n1CCS2,1,test
O=[N+]([O-])c1ccc2c3c(cccc13)C=C2,1,test
O=c1oc2c([N+](=O)[O-])ccc3ccc4cccc1c4c32,1,test
CCN(CC)c1ccc2nc3c4ccccc4c(=[NH2+])cc-3oc2c1,1,test
Cn1c([N+](=O)[O-])nc2ccccc21,1,test
Cc1cc([N+](=O)[O-])c(C)c2c1[nH]c1ccc(O)cc12,1,test
Nc1ccc(-c2ccc(N)c([N+](=O)[O-])c2)cc1,1,test
O=[N+]([O-])c1ccc2c3c1ccc1cccc(c13)C(O)C2O,1,test
O=C(Cl)c1cccc([N+](=O)[O-])c1,1,test
O=C(Cl)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2ccc3cccc4c5ccccc5c1c2c34,1,test
O=C(Cl)c1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc2ccc3c4c(cc5ccc1c2c53)CCC=C4,1,test
O=C(CBr)c1oc([N+](=O)[O-])c(Cl)c1Cl,1,test
O=[N+]([O-])c1ccccc1CCl,1,test
O=C(O)CNC(=O)c1ccc([N+](=O)[O-])cc1,1,test
Nc1cc2c(cc1[N+](=O)[O-])[nH]c1ccccc12,1,test
O=C1c2cc([N+](=O)[O-])cc([N+](=O)[O-])c2-c2c1cc([N+](=O)[O-])cc2[N+](=O)[O-],1,test
O=C(CBr)c1oc([N+](=O)[O-])c(-c2ccccc2)c1-c1ccccc1,1,test
O=[N+]([O-])c1ccc2ccc3cc4c(c5ccc1c2c35)CCCC4,1,test
CC(C)COC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
CCC(C)N=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c2ccccc2c1,1,test
CCCCOC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
CC1CN=C(O)N1c1ncc([N+](=O)[O-])s1,1,test
Nc1ccc([N+](=O)[O-])cc1,1,test
CC(=O)c1oc([N+](=O)[O-])c(Cl)c1Cl,1,test
O=[N+]([O-])c1ccc2c([N+](=O)[O-])cc3c([N+](=O)[O-])cccc3c2c1,1,test
Cc1ccccc1N=C(O)c1csc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cc2cc3ccc4cccc5ccc(c2o1)c3c45,1,test
O=[N+]([O-])c1ccc2c(c1)sc1ccccc12,1,test
CC(O)=Nc1ccc(C=Nn2nnc3ccnc-3c2O)cc1,1,test
O=[N+]([O-])c1ccc(Sc2ccccc2)cc1,1,test
Cc1cc([N+](=O)[O-])c(C)c2c1[nH]c1ccc([N+](=O)[O-])cc12,1,test
O=[N+]([O-])c1ccc2c3ccccc3c3cccc4ccc1c2c43,1,test
O=[N+]([O-])c1ccc2c(ccc3ccccc32)c1,1,test
Nc1cccc([N+](=O)[O-])c1N,1,test
Cc1ccc([N+](=O)[O-])c2ccccc12,1,test
CN(C)CCCNc1c2ccccc2nc2c([N+](=O)[O-])cccc12,1,test
O=[N+]([O-])c1cc2c3c(ccc2c2ccccc12)C(O)C(O)C=C3,1,test
O=[N+]([O-])c1ccc2c3c(cccc13)CC2,1,test
CCCC(=O)OCc1ccc([N+](=O)[O-])cc1,1,test
CC(C)N=C(NC(C)C)OCc1ccc([N+](=O)[O-])cc1,1,test
C[N+](C)(C)CCNCCc1ccc(N=Nc2ccc([N+](=O)[O-])cc2Cl)cc1,1,test
O=[N+]([O-])c1ccc2ccc3cccc4ccc1c2c34,1,test
O=[N+]([O-])c1ccc2cc([N+](=O)[O-])c3ccc([N+](=O)[O-])cc3c2c1,1,test
CCN(CC)c1ccc(N=Nc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1ccc2c3ccccc3c3c([N+](=O)[O-])ccc4ccc1c2c43,1,test
Nc1nc(-c2ccc([N+](=O)[O-])cc2)nc2c1ncn2-c1ccc([N+](=O)[O-])cc1,1,test
COc1cc([N+](=O)[O-])ccc1N=C(C)O,1,test
O=[N+]([O-])c1ccc2ccc3cccc4cc(O)c1c2c34,1,test
CC(=O)c1c([N+](=O)[O-])cc([N+](=O)[O-])n1[N+](=O)[O-],1,test
Nc1cc([N+](=O)[O-])ccc1-c1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc2ccc3c4c(ccc1c24)c1ccccc1[n+]3[O-],1,test
O=[N+]([O-])c1ccc2c(c1)Cc1cc(O)ccc1-2,1,test
Cn1cnc(-c2ccccc2)c1[N+](=O)[O-],1,test
Cn1c2cccc(=[NH2+])c-2cc2ccccc21,1,test
O=[N+]([O-])c1ccc2cccc3c2c1-c1ccccc1-3,1,test
O=C(C=Cc1ccccc1)c1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc(F)c([N+](=O)[O-])c1,1,test
COc1nsc2c([N+](=O)[O-])cccc12,1,test
CCCN=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=C(O)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1,1,test
CN(C)c1ccc(C=Cc2ccc([N+](=O)[O-])cc2)cc1,1,test
O=[N+]([O-])c1ccc2[nH]ccc2c1,1,test
COc1ccc(NC(=O)c2csc([N+](=O)[O-])c2)cc1,1,test
O=C(O)c1cc([N+](=O)[O-])ccc1[N+](=O)[O-],1,test
O=[N+]([O-])c1cccc2c1oc1ccccc12,1,test
O=[N+]([O-])c1ccc(OC2CO2)cc1,1,test
Nc1ccc(Cl)cc1[N+](=O)[O-],1,test
Cc1ccc(NN=NCc2ccc([N+](=O)[O-])cc2)cc1,1,test
Cc1ccc(N=[N+]([O-])c2ccc(C)c(N)c2)cc1N,1,test
Nc1ccc2ccccc2c1N=Nc1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc2ccc3c4c(cc5ccc1c2c53)CCCC4,1,test
CC(C)(C)CN=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2c(ccc3c4ccccc4ccc23)c1,1,test
O=c1cc(-c2ccccc2)cc(-c2ccccc2)n1N=Cc1ccc([N+](=O)[O-])o1,1,test
O=C(C=Cc1ccc([N+](=O)[O-])cc1)c1ccccc1,1,test
Cc1c(N)cc(N)cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1cccc2ccccc12,1,test
CC(C)N=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
Nc1ccc(NCCO)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cc(N(CCO)CCO)ccc1NCCO,1,test
O=[N+]([O-])c1ccc2sc3ccccc3c2c1,1,test
O=[N+]([O-])c1ccc2c(c1)c([N+](=O)[O-])cc1c([N+](=O)[O-])cccc12,1,test
CCc1cccc(N=C(O)c2csc([N+](=O)[O-])c2)c1,1,test
O=[N+]([O-])c1cc2cccc3cccc(o1)c32,1,test
CN(Cc1ccc([N+](=O)[O-])cc1)N=O,1,test
CCCCC(CC)COC(=O)c1ccc(N(C)C)c([N+](=O)[O-])c1,1,test
Nc1c(C(=O)O)cc([N+](=O)[O-])c2c1C(=O)c1ccccc1C2=O,1,test
O=[N+]([O-])c1ccc(C2N=C(O)c3ccccc3N2)s1,1,test
Nc1ccc2c([nH]c3ccccc32)c1[N+](=O)[O-],1,test
Nc1nc(-c2ccc([N+](=O)[O-])cc2)nc2c1ncn2C1OC(CO)C(O)C1O,1,test
CN(C)CCCN=c1c2ccccc2[nH]c2cccc([N+](=O)[O-])c12,1,test
O=[N+]([O-])c1ccc2c(c1)CCc1ccccc1-2,1,test
O=[N+]([O-])c1ccc2ccc3ccccc3c2c1,1,test
CN(C)C(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2c(c1)-c1ccc([N+](=O)[O-])c3cccc-2c13,1,test
O=C(C=Cc1ccc([N+](=O)[O-])cc1)c1ccc([N+](=O)[O-])cc1,1,test
Cn1c2cc(=[NH2+])ccc-2cc2ccccc21,1,test
Cc1ccc(C)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2c([N+](=O)[O-])cc3cccc([N+](=O)[O-])c3c2c1,1,test
CN(C)[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc(CCO)cc1,1,test
CC(C)=[N+]([O-])[O-],1,test
O=[N+]([O-])c1ccc2c3c(cccc13)-c1ccccc1-2,1,test
O=[N+]([O-])c1cccc([N+](=O)[O-])c1,1,test
Nc1ncnc2c1nc(-c1ccc([N+](=O)[O-])cc1)n2-c1ccc([N+](=O)[O-])cc1,1,test
CC1CS(=O)(=O)CCN1N=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2ccc3c4ccccc4[n+]([O-])c4ccc1c2c34,1,test
Nc1cc(Cl)c([N+](=O)[O-])cc1O,1,test
Cc1c([N+](=O)[O-])cnc2c1nc1c(C)cccn12,1,test
O=[N+]([O-])c1cc2c3c(cccc3c1)-c1ccccc1-2,1,test
CC1(c2ccc([N+](=O)[O-])cc2)CO1,1,test
O=[N+]([O-])c1ccccc1SSC(Cl)=C(Cl)C(Cl)=C(Cl)Cl,1,test
O=C(O)c1cccc([N+](=O)[O-])c1,1,test
O=C1Nc2ccc([N+](=O)[O-])cc2C1=O,1,test
COc1nsc2cccc([N+](=O)[O-])c12,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1,1,test
COc1cccc(N=C(O)c2csc([N+](=O)[O-])c2)c1,1,test
NNc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])C(Cl)(Cl)Cl,1,test
CCc1ccc(NC(=O)c2csc([N+](=O)[O-])c2)cc1,1,test
Nc1ccc(-c2ccc(N)cc2[N+](=O)[O-])cc1,1,test
Nc1ccc(N)c([N+](=O)[O-])c1,1,test
O=CC=Cc1ccc([N+](=O)[O-])o1,1,test
Cc1c([N+](=O)[O-])cccc1[N+](=O)[O-],1,test
CCCCCN=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2cc([N+](=O)[O-])c([N+](=O)[O-])cc2c1,1,test
O=[N+]([O-])c1ccc2c(c1)CC=C2,1,test
O=Nc1ccc2ccc3c([N+](=O)[O-])ccc4ccc1c2c43,1,test
COC(=O)c1oc([N+](=O)[O-])c(Cl)c1Cl,1,test
CCc1ccccc1N=C(O)c1csc([N+](=O)[O-])c1,1,test
Cc1cc(C)c([N+](=O)[O-])cc1[N+](=O)[O-],1,test
Cc1ccc(N)cc1[N+](=O)[O-],1,test
CCCCCOC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
COc1ccc2cc([N+](=O)[O-])oc3cccc1c23,1,test
CN=C(O)C=C(C)OP(=O)(OC)OC,1,test
Nc1cc([N+](=O)[O-])cc([N+](=O)[O-])c1,1,test
COc1ccc([N+](=O)[O-])c2c(=NCCCN(C)C)c3ccccc3[nH]c12,1,test
CC(C)(N=O)[N+](=O)[O-],1,test
CN(CCO[N+](=O)[O-])[N+](=O)[O-],1,test
O=[N+]([O-])c1cccc(O)c1,1,test
Nc1ccc([N+](=O)[O-])cc1Cl,1,test
O=C1N=C(O)c2ccc([N+](=O)[O-])cc21,1,test
Cc1cc(N)c([N+](=O)[O-])cc1N,1,test
O=[N+]([O-])c1ccc2c[nH]nc2c1,1,test
O=[N+]([O-])c1cc2ccc3cccc4ccc(c1)c2c34,1,test
O=[N+]([O-])c1ccc2ccc3nc4ccccc4c4ccc1c2c34,1,test
O=[N+]([O-])c1ccc(C=NN2CCCN=C2O)o1,1,test
O=[N+]([O-])c1cnc2c(c1)nc1ccccn12,1,test
CCS(=O)(=O)CCn1c([N+](=O)[O-])cnc1C,1,test
COc1ccc([N+](=O)[O-])cc1N=Nc1c(O)c(C(O)=Nc2cccc([N+](=O)[O-])c2)cc2ccccc12,1,test
[O-][N+](O)=CCO,1,test
CC(C)(Cl)[N+](=O)[O-],1,test
COc1ccc(N=[N+]([O-])c2ccc(OC)cc2)cc1,1,test
O=[N+]([O-])c1cccc2c([N+](=O)[O-])cccc12,1,test
COc1ccccc1N=C(O)c1csc([N+](=O)[O-])c1,1,test
COc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
COc1ccc2c(ccc3oc([N+](=O)[O-])cc32)c1,1,test
O=[N+]([O-])c1ccc2ccc3cc4c(c5ccc1c2c35)CCC=C4,1,test
Cc1ccc(N=Nc2c(O)ccc3ccccc23)c([N+](=O)[O-])c1,1,test
Nc1cc2[nH]c3ccccc3c2cc1[N+](=O)[O-],1,test
N#CCc1ccccc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc2cc3ccccc3cc2c1,1,test
O=C1c2cc([N+](=O)[O-])ccc2-c2c1cc([N+](=O)[O-])cc2[N+](=O)[O-],1,test
CC(=O)OC(OC(C)=O)c1ccc([N+](=O)[O-])o1,1,test
COC(=O)c1ccc([N+](=O)[O-])o1,1,test
COC(=O)C(C)=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1nccn1CC(O)=NCc1ccccc1,1,test
Cc1ncc([N+](=O)[O-])n1CCO,1,test
O=[N+]([O-])c1cccc(Br)c1,1,test
O=C1c2cc([N+](=O)[O-])ccc2-c2ccc([N+](=O)[O-])cc21,1,test
O=[N+]([O-])c1cc2c(cc1Cl)Oc1cc(Cl)c(Cl)cc1O2,1,test
O=[N+]([O-])c1cc(C(O)=Nc2ccccc2)cs1,1,test
O=[N+]([O-])c1ccccc1CCO,1,test
O=[N+]([O-])c1ccc(-c2ccc([N+](=O)[O-])cc2[N+](=O)[O-])c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2c3c([N+](=O)[O-])ccc4cccc(c5cccc1c52)c43,1,test
O=c1oc2ccc([N+](=O)[O-])cc2c2ccccc12,1,test
O=[N+]([O-])c1ccc(Oc2ccccc2)cc1,1,test
Cc1ccc(C)c2c1[nH]c1ccc([N+](=O)[O-])cc12,1,test
O=[N+]([O-])c1ccc2c(c1)C(O)c1ccccc1-2,1,test
O=Nc1c(-c2ccc([N+](=O)[O-])cc2)nc2sccn12,1,test
Nc1ccc([N+](=O)[O-])cc1O,1,test
COc1cc([N+](=O)[O-])ccc1N,1,test
CCOc1ccc([N+](=O)[O-])cc1,1,test
O=c1oc2cc([N+](=O)[O-])ccc2c2ccccc12,1,test
O=[N+]([O-])c1cc(C(O)=Nc2ccccc2Cl)cs1,1,test
Nc1cc([N+](=O)[O-])c(N)cc1F,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c(-c2ccccc2)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc(C=Cc2cccc(Cl)c2)cc1,1,test
O=[N+]([O-])c1ccc(-c2ccc([N+](=O)[O-])c([N+](=O)[O-])c2)c([N+](=O)[O-])c1,1,test
CN=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2ccc3c4ccccc4nc4ccc1c2c43,1,test
CCC(C)OC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2ccc3c(O)ccc4ccc1c2c43,1,test
CN1CNc2c1n[c-][n+](O)c2N,1,test
CC(=O)OCN(C)[N+](=O)[O-],1,test
CC(Cl)(Cl)[N+](=O)[O-],1,test
O=C(C(Cl)Cl)N1CCN(c2ncc([N+](=O)[O-])s2)C1=O,1,test
O=[N+]([O-])c1ccc2ccc3c([N+](=O)[O-])c4ccccc4c4ccc1c2c34,1,test
CN(C)CCCN=c1c2ccccc2[nH]c2c(N(CCO)CCO)ccc([N+](=O)[O-])c12,1,test
Cc1ccc(NC(=O)c2csc([N+](=O)[O-])c2)cc1,1,test
O=[N+]([O-])c1ccc(Nc2ccc(Cl)cc2)cc1,1,test
NC(CCC(O)=NC(CSc1ccc([N+](=O)[O-])cc1[N+](=O)[O-])C(O)=NCC(=O)O)C(=O)O,1,test
O=[N+]([O-])c1cc(-c2ccccc2)cc([N+](=O)[O-])c1O,1,test
Cn1c2ccccc2c(=[NH2+])c2ccccc21,1,test
CCOc1ccccc1[N+](=O)[O-],1,test
COC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
Nc1c([N+](=O)[O-])cccc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc2nc[nH]c2c1,1,test
O=[N+]([O-])c1cc[n+]([O-])cc1,1,test
O=[N+]([O-])c1ccc2c3cccc4cccc(c5cccc1c52)c43,1,test
CC(O)=NN=Cc1c[n+]([O-])c2ccccc2[n+]1[O-],1,test
O=[N+]([O-])c1ccc2c(c1)Cc1ccccc1-2,1,test
O=[N+]([O-])c1ccc2c(c1)CCc1cc3c(cc1-2)CCc1ccccc1-3,1,test
O=C(Nc1ccc(F)cc1)c1csc([N+](=O)[O-])c1,1,test
Nc1ccc(O)c([N+](=O)[O-])c1,1,test
CSc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=[N+]([O-])c1c2c3c(ccc4cccc(c43)CC2)c2ccccc12,1,test
O=[N+]([O-])c1ccc(-c2nc(=NCCO)c3ccccc3[nH]2)s1,1,test
O=[N+]([O-])c1ccc2c(ccc3cc([N+](=O)[O-])ccc32)c1,1,test
Cc1ccc(Nc2ccc([N+](=O)[O-])cc2)cc1,1,test
CCN=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1c2ccccc2c2ccc3cccc4ccc1c2c43,1,test
CC(=O)c1cccc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cc(O)c2ccc3cccc4ccc1c2c34,1,test
Cn1cc2cccc([N+](=O)[O-])c2n1,1,test
O=C(Nc1ccc(Br)cc1)c1csc([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1cc2c(cc3ccc4cccc5ccc2c3c45)o1,1,test
O=[N+]([O-])c1ccc2c(c1)Oc1ccccc1O2,1,test
N=C(O)NN=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])C([N+](=O)[O-])([N+](=O)[O-])[N+](=O)[O-],1,test
Cc1c(N)cc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
[O-][N+](=Nc1ccccc1)c1ccccc1,1,test
O=[N+]([O-])c1ccc2c(O)cc3cccc4ccc1c2c43,1,test
O=[N+]([O-])c1ccccc1Br,1,test
CC(C)(C)OC(=O)C=Cc1ccc([N+](=O)[O-])o1,1,test
Cn1cnc([N+](=O)[O-])c1Sc1ncnc2[nH]cnc12,1,test
Nc1ccc2ccc3ccc([N+](=O)[O-])c4ccc1c2c34,1,test
CC(C)(C)N=C(O)C=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc2c(c1)Cc1cccc([N+](=O)[O-])c1-2,1,test
COC(O)=NN=Cc1c[n+]([O-])c2ccccc2[n+]1[O-],1,test
O=[N+]([O-])c1ccc2c(c1)CCc1cc([N+](=O)[O-])ccc1-2,1,test
O=[N+]([O-])c1ccc2[nH]c3ccccc3c2c1,1,test
O=[N+]([O-])c1ccc2ccc3c4ccccc4c([N+](=O)[O-])c4ccc1c2c43,1,test
Cn1ccnc1[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc(S)cc1,1,test
O=[N+]([O-])c1cnc(N2CCN=C2O)s1,1,test
O=[N+]([O-])c1cc2c3c(ccc4cc([N+](=O)[O-])cc(c43)CC2)c1,1,test
O=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1ccc(Nc2ccc(Nc3ccccc3)c(S(=O)(=O)O)c2)c([N+](=O)[O-])c1,1,test
CNc1ccc(N(CCO)CCO)cc1[N+](=O)[O-],1,test
COC(=O)c1ccc([N+](=O)[O-])c2c(=NCCCN(C)C)c3ccccc3[nH]c12,1,test
Cc1c(N)cc([N+](=O)[O-])c(N)c1C,1,test
Cc1cc([N+](=O)[O-])ccc1[N+](=O)[O-],1,test
O=C(OCC1CO1)c1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc(-c2ccccc2)cc1,1,test
O=[N+]([O-])c1cc([N+](=O)[O-])c2ccc3cccc4ccc1c2c43,1,test
O=[N+]([O-])c1ccc2c(ccc3cc4c(ccc5ccccc54)cc32)c1,1,test
O=[N+]([O-])c1cc(C(O)=Nc2cccc(F)c2)cs1,1,test
O=[N+]([O-])c1ccc2c(c1)oc1ccccc12,1,test
Nc1ccc(-c2ccc(N)c([N+](=O)[O-])c2)cc1[N+](=O)[O-],1,test
Nc1ccc(CO)c([N+](=O)[O-])c1,1,test
O=[N+]([O-])c1ccc2c3c(ccc([N+](=O)[O-])c13)-c1ccccc1-2,1,test
Nc1cc([N+](=O)[O-])ccc1CO,1,test
Cc1ncc([N+](=O)[O-])n1C,1,test
CC(=O)N(O)c1ccc([N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc2ncccc2c1,1,test
O=[N+]([O-])c1cc[n+]([O-])c2ccccc12,1,test
Nc1ccc([N+](=O)[O-])c(N)c1,1,test
O=C(c1ccc([N+](=O)[O-])cc1)C1OC1c1ccccc1,1,test
O=[N+]([O-])c1ccc(-c2csc(N=CO)n2)o1,1,test
O=[N+]([O-])c1ccc2c(c1)Oc1cc(Cl)c(Cl)cc1O2,1,test
O=[N+]([O-])c1ccc(-c2nc3sccn3c2[N+](=O)[O-])cc1,1,test
O=[N+]([O-])c1ccc2c(c1)-c1ccccc1C2,1,test
NC(=NO)c1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])c1cccc2c1[nH]c1ccccc12,1,test
CCn1c2cc(=N)ccc-2c2ccc([NH3+])cc2c1-c1ccccc1,1,test
N=c1[nH]cc([N+](=O)[O-])s1,1,test
CCCCN(N=O)C(=N)N[N+](=O)[O-],1,test
O=[N+]([O-])c1ccc2c(O)nnc(O)c2c1,1,test
CCN(N=O)C(=N)N[N+](=O)[O-],1,test
N=C(NO)c1ccccc1,1,test
N=c1[nH]c2ccc([N+](=O)[O-])cc2s1,1,test
CN(N=O)C(=N)N[N+](=O)[O-],1,test
Nc1c2ncnc-2[nH]c[n+]1[O-],1,test
O=[N+]([O-])c1ccc(CBr)cc1,1,test
CCOP(=O)(OCC)Oc1ccc([N+](=O)[O-])cc1,1,test
CCCN(C(=N)N=NO)[N+](=O)[O-],1,test
CCCCCN(N=O)C(=N)N[N+](=O)[O-],1,test
Cn1c([N+](=O)[O-])cnc1-c1n[nH]c(=N)s1,1,test
Nc1ncnc2nc(-c3ccc([N+](=O)[O-])cc3)[nH]c12,1,test
NC(CSc1ccc([N+](=O)[O-])cc1[N+](=O)[O-])C(=O)O,1,test
C[N+]([O-])(CCCl)CCCl,1,test
O=[N+]([O-])c1ccc(NC(O)=NCCCl)s1,1,test
CC(C)CN(N=O)C(=N)N[N+](=O)[O-],1,test
