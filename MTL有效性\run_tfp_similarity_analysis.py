import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tfp_similarity_analysis import TFPSimilarityAnalyzer

def main():
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建分析器
    analyzer = TFPSimilarityAnalyzer()
    
    # 创建输出目录
    output_dir = 'tfp_similarity_results'
    os.makedirs(output_dir, exist_ok=True)
    
    print("=" * 60)
    print("TFP-G Parameter Similarity Analysis")
    print("=" * 60)
    
    # ==================== 任务A：不同FC层配置的相似性分析 ====================
    print("\n任务A：分析不同FC层配置的MGA模型TFP-G参数相似性")
    print("-" * 50)
    
    # 基础模型配置（3层FC）
    base_config = {
        'in_feats': 40,
        'rgcn_hidden_feats': [128, 128],
        'n_tasks': 5,  # MTL-scr有5个任务
        'classifier_hidden_feats': 128,
        'rgcn_drop_out': 0.2,
        'dropout': 0.2,
        'loop': True,
        'return_mol_embedding': False,
        'return_weight': False
    }
    
    # 不同FC层配置
    fc_configs = {
        '1_layer_FC': {
            'num_layers': 1,
            'hidden_dims': [128]
        },
        '2_layer_FC': {
            'num_layers': 2,
            'hidden_dims': [128, 128]
        },
        '4_layer_FC': {
            'num_layers': 4,
            'hidden_dims': [128, 128, 128, 64]
        },
        '5_layer_FC': {
            'num_layers': 5,
            'hidden_dims': [128, 128, 128, 64, 32]
        }
    }
    
    # 基础模型路径
    base_model_path = 'model/MTL-scr_early_stop.pth'
    
    if os.path.exists(base_model_path):
        try:
            similarity_matrix_a, model_names_a = analyzer.analyze_fc_layer_similarity(
                base_model_path, base_config, fc_configs, output_dir
            )
            print(f"任务A完成！结果保存在 {output_dir}/fc_layer_similarity_heatmap.png")
            
            # 打印相似性结果
            print("\nFC层配置相似性矩阵:")
            for i, name1 in enumerate(model_names_a):
                for j, name2 in enumerate(model_names_a):
                    if i < j:  # 只打印上三角
                        print(f"{name1} vs {name2}: {similarity_matrix_a[i, j]:.4f}")
        
        except Exception as e:
            print(f"任务A执行出错: {str(e)}")
    else:
        print(f"基础模型文件不存在: {base_model_path}")
    
    # ==================== 任务B：不同毒性任务的相似性分析 ====================
    print("\n任务B：分析不同毒性任务之间的TFP-G参数相似性")
    print("-" * 50)
    
    # 不同任务的模型路径
    model_paths_dict = {
        'MTL-scr': 'model/MTL-scr_early_stop.pth',
        'STL-scr': 'model/STL-scr_early_stop.pth',
        'FishLC50': 'model/FishLC50_early_stop.pth',
        'FishEL_NOEC': 'model/FishEL_NOEC_early_stop.pth',
        'MTL': 'model/MTL_early_stop.pth'
    }
    
    # 不同任务的模型配置
    model_configs_dict = {
        'MTL-scr': {
            'in_feats': 40,
            'rgcn_hidden_feats': [128, 128],
            'n_tasks': 5,
            'classifier_hidden_feats': 128,
            'rgcn_drop_out': 0.2,
            'dropout': 0.2,
            'loop': True,
            'return_mol_embedding': False,
            'return_weight': False
        },
        'STL-scr': {
            'in_feats': 40,
            'rgcn_hidden_feats': [128, 128],
            'n_tasks': 1,
            'classifier_hidden_feats': 128,
            'rgcn_drop_out': 0.2,
            'dropout': 0.2,
            'loop': True,
            'return_mol_embedding': False,
            'return_weight': False
        },
        'FishLC50': {
            'in_feats': 40,
            'rgcn_hidden_feats': [128, 128],
            'n_tasks': 1,
            'classifier_hidden_feats': 128,
            'rgcn_drop_out': 0.2,
            'dropout': 0.2,
            'loop': True,
            'return_mol_embedding': False,
            'return_weight': False
        },
        'FishEL_NOEC': {
            'in_feats': 40,
            'rgcn_hidden_feats': [128, 128],
            'n_tasks': 1,
            'classifier_hidden_feats': 128,
            'rgcn_drop_out': 0.2,
            'dropout': 0.2,
            'loop': True,
            'return_mol_embedding': False,
            'return_weight': False
        },
        'MTL': {
            'in_feats': 40,
            'rgcn_hidden_feats': [128, 128],
            'n_tasks': 5,
            'classifier_hidden_feats': 128,
            'rgcn_drop_out': 0.2,
            'dropout': 0.2,
            'loop': True,
            'return_mol_embedding': False,
            'return_weight': False
        },
        'default': {
            'in_feats': 40,
            'rgcn_hidden_feats': [128, 128],
            'n_tasks': 1,
            'classifier_hidden_feats': 128,
            'rgcn_drop_out': 0.2,
            'dropout': 0.2,
            'loop': True,
            'return_mol_embedding': False,
            'return_weight': False
        }
    }
    
    try:
        # 分析整体TFP-G参数相似性
        similarity_matrix_b, task_names_b = analyzer.analyze_task_similarity(
            model_paths_dict, model_configs_dict, output_dir
        )
        
        if similarity_matrix_b is not None:
            print(f"任务B完成！结果保存在 {output_dir}/task_similarity_heatmap.png")
            
            # 打印相似性结果
            print("\n毒性任务相似性矩阵:")
            for i, name1 in enumerate(task_names_b):
                for j, name2 in enumerate(task_names_b):
                    if i < j:  # 只打印上三角
                        print(f"{name1} vs {name2}: {similarity_matrix_b[i, j]:.4f}")
        
        # 分析注意力权重相似性
        print("\n分析注意力权重相似性...")
        attention_similarity_matrix, attention_task_names = analyzer.analyze_attention_weights_similarity(
            model_paths_dict, model_configs_dict, output_dir
        )
        
        if attention_similarity_matrix is not None:
            print(f"注意力权重相似性分析完成！结果保存在 {output_dir}/attention_similarity_heatmap.png")
    
    except Exception as e:
        print(f"任务B执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("分析完成！所有结果保存在:", output_dir)
    print("=" * 60)

if __name__ == "__main__":
    main()
