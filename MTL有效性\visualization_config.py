"""
可视化配置文件

统一管理任务A和任务B的图片尺寸和样式设置
"""

# 统一的图片尺寸配置
FIGURE_SIZES = {
    'heatmap': (12, 10),        # 热力图统一尺寸
    'comparison': (16, 8),      # 对比图尺寸
    'small_heatmap': (8, 6),    # 小型热力图
    'large_heatmap': (14, 12)   # 大型热力图
}

# 字体配置
FONT_CONFIG = {
    'family': 'serif',
    'serif': 'Times New Roman',
    'mathtext_fontset': 'stix',
    'unicode_minus': False,
    'dpi': 300
}

# 热力图样式配置
HEATMAP_STYLE = {
    'annot': True,              # 显示数值
    'fmt': '.3f',              # 数值格式（3位小数）
    'cmap': 'Blues',           # 默认颜色映射
    'linewidths': 0,           # 无网格线
    'square': True,            # 方形单元格
    'cbar_kws': {              # 颜色条设置
        'shrink': 0.8,
        'aspect': 20
    }
}

# 标题和标签样式
TEXT_STYLE = {
    'title_fontsize': 16,
    'title_fontweight': 'bold',
    'title_pad': 20,
    'label_fontsize': 12,
    'label_fontweight': 'bold',
    'tick_labelsize': 10
}

# 任务特定配置
TASK_SPECIFIC = {
    'task_a': {
        'title_prefix': 'TFP-G Parameter Similarity',
        'output_dir': 'task_a_results',
        'color_scheme': 'Blues'
    },
    'task_b': {
        'title_prefix': 'Toxicity Task Analysis',
        'output_dir': 'task_b_results',
        'color_scheme': 'Blues'
    }
}

def get_figure_size(size_type='heatmap'):
    """获取图片尺寸"""
    return FIGURE_SIZES.get(size_type, FIGURE_SIZES['heatmap'])

def get_heatmap_style():
    """获取热力图样式配置"""
    return HEATMAP_STYLE.copy()

def get_text_style():
    """获取文本样式配置"""
    return TEXT_STYLE.copy()

def apply_font_config():
    """应用字体配置到matplotlib"""
    import matplotlib.pyplot as plt
    
    plt.rcParams['font.family'] = FONT_CONFIG['family']
    plt.rcParams['font.serif'] = FONT_CONFIG['serif']
    plt.rcParams['mathtext.fontset'] = FONT_CONFIG['mathtext_fontset']
    plt.rcParams['axes.unicode_minus'] = FONT_CONFIG['unicode_minus']
    plt.rcParams['figure.dpi'] = FONT_CONFIG['dpi']

def get_task_config(task_name):
    """获取任务特定配置"""
    return TASK_SPECIFIC.get(task_name, TASK_SPECIFIC['task_a'])
