FishLC50:{'max_depth': 9, 'min_child_samples': 60, 'reg_lambda': 1e-05, 'reg_alpha': 1e-05, 'learning_rate': 0.05, 'n_estimators': 280, 'feature_fraction': 0.85, 'bagging_fraction': 0.75, 'num_leaves': 111}
FishEL_NOEC:{'max_depth': 8, 'min_child_samples': 80, 'reg_lambda': 0.1, 'reg_alpha': 1, 'learning_rate': 0.001, 'n_estimators': 220, 'feature_fraction': 0.75, 'bagging_fraction': 0.75, 'num_leaves': 51}
DMRepNOEC:{'max_depth': 7, 'min_child_samples': 30, 'reg_lambda': 1, 'reg_alpha': 0.01, 'learning_rate': 0.05, 'n_estimators': 180, 'feature_fraction': 0.8, 'bagging_fraction': 0.85, 'num_leaves': 61}
DMImbEC50:{'max_depth': 7, 'min_child_samples': 70, 'reg_lambda': 0.01, 'reg_alpha': 1e-05, 'learning_rate': 0.05, 'n_estimators': 180, 'feature_fraction': 0.85, 'bagging_fraction': 0.8, 'num_leaves': 61}
AlaGroErC50:{'max_depth': 7, 'min_child_samples': 40, 'reg_lambda': 1e-05, 'reg_alpha': 0.01, 'learning_rate': 0.05, 'n_estimators': 140, 'feature_fraction': 0.85, 'bagging_fraction': 0.85, 'num_leaves': 121}
