"""
任务B直接运行脚本

直接运行毒性任务间相似性分析，无需交互
"""

import os
import sys
import matplotlib.pyplot as plt

# 添加路径以导入模块
sys.path.append('.')
from task_b.toxicity_task_analyzer import ToxicityTaskAnalyzer


def main():
    """主函数 - 直接运行任务B"""
    # 设置字体为Times New Roman (与任务A字体设置一致)
    plt.rcParams['font.family'] = 'serif'
    plt.rcParams['font.serif'] = 'Times New Roman'
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['axes.unicode_minus'] = False
    
    print("=" * 60)
    print("任务B：毒性任务间相似性分析 - 直接运行模式")
    print("=" * 60)
    
    # 创建分析器
    analyzer = ToxicityTaskAnalyzer()
    
    # 更新模型路径
    updated_model_paths = {}
    for task_name, model_path in analyzer.model_paths.items():
        updated_model_paths[task_name] = f"../{model_path}"
    
    try:
        # 直接执行分析
        results = analyzer.analyze_task_similarity(
            custom_model_paths=updated_model_paths
        )
        
        # 打印结果
        analyzer.print_similarity_results(results)
        
        print("\n✅ 分析完成！结果已保存到 task_b_results/ 目录")
        
    except Exception as e:
        print(f"❌ 分析出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
