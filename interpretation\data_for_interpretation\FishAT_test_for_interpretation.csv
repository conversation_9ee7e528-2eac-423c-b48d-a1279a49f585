﻿smiles,FishAT,group
CC(O)CO,0,test
FC(F)(Cl)C(F)(Cl)Cl,1,test
FC(F)(F)c1ccccc1,1,test
Cc1c([N+](=O)[O-])cc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
O=C(O)C(F)(F)C(F)(F)C(F)(F)F,0,test
O=C(O)CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,1,test
CCCCCCCCCCCC#N,1,test
Clc1cccc(-c2ccc(Cl)c(Cl)c2Cl)c1Cl,1,test
O=S(=O)(O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,0,test
CC(C)(C)C(O)C(Oc1ccc(Cl)cc1)n1cncn1,1,test
CCC(C)(C)C(=O)O[C@H]1C[C@@H](C)C=C2C=C[C@H](C)[C@H](CC[C@@H]3C[C@@H](O)CC(=O)O3)[C@H]21,1,test
N#CN=C1SCCN1Cc1ccc(Cl)nc1,1,test
O=[N+]([O-])N=C1NCCN1Cc1ccc(Cl)nc1,0,test
CCOC(=O)OC1=C(c2cc(C)ccc2C)C(=O)NC12CCC(OC)CC2.Cc1cc(C(F)(C(F)(F)F)C(F)(F)F)ccc1NC(=O)c1cccc(I)c1C(=O)NC(C)(C)CS(C)(=O)=O,1,test
O=C(Nc1ccc([N+](=O)[O-])cc1Cl)c1cc(Cl)ccc1O,1,test
NC(=O)c1ccccc1,0,test
OCC(O)CO,0,test
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)ccc(Cl)c4[C@@](C)(O)[C@H]3C[C@@H]12,1,test
NC(=O)N/N=C/c1ccc([N+](=O)[O-])o1,1,test
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H]4C[C@@H]([C@H]5O[C@@H]45)[C@@H]3[C@@]1(Cl)C2(Cl)Cl,1,test
CNC(=O)Oc1cccc2ccccc12,1,test
O=Cc1ccc(Oc2ccccc2)cc1,1,test
Oc1c(Cl)cc(Cl)c(Cl)c1Cc1c(O)c(Cl)cc(Cl)c1Cl,1,test
COc1ccc(C(c2ccc(OC)cc2)C(Cl)(Cl)Cl)cc1,1,test
ClCCl,0,test
C[N+](C)(C)C,0,test
O=[N+]([O-])C(Cl)(Cl)Cl,1,test
C#CC(C)(O)CC,0,test
C=CC(C)(O)CCC=C(C)C,1,test
ClCC(Cl)Cl,1,test
CC(C)[N+](=O)[O-],0,test
CC(C)(OOC(C)(C)c1ccccc1)c1ccccc1,1,test
CCCOC(=O)C1c2cc3c(cc2CC(C)C1C(=O)OCCC)OCO3,1,test
COP(=S)(OC)SCn1nnc2ccccc2c1=O,1,test
O=c1n(Cl)c(=O)n(Cl)c(=O)n1Cl,1,test
Nc1ccccc1[N+](=O)[O-],1,test
O=Cc1ccccc1Cl,1,test
COc1ccccc1[N+](=O)[O-],0,test
Oc1ccc(-c2ccccc2)c(Cl)c1,1,test
CC(=O)CC(=O)c1ccccc1,1,test
O=C(O)CCCOc1ccc(Cl)cc1Cl,1,test
c1ccc2sc(SNC3CCCCC3)nc2c1,1,test
Cc1ccc(C)c(C)c1,1,test
Clc1cc(Cl)c(Cl)cc1Cl,1,test
OCC(O)CCl,0,test
Oc1ccc(Cl)cc1Cc1cc(Cl)ccc1O,1,test
Cc1cc(S(=O)(=O)O)ccc1N,0,test
O=C(Cl)c1ccccc1,1,test
O=[N+]([O-])c1ccc(Cl)c(Cl)c1,1,test
Cc1ccc(C(=O)O)cc1,1,test
O=[N+]([O-])c1ccc([N+](=O)[O-])cc1,1,test
CNc1ccccc1,1,test
CC(C)OC(=O)Nc1cccc(Cl)c1,1,test
OCCN(CCO)CCO,0,test
CCCCc1ccc(N)cc1,1,test
Cc1ccc(O)c(C)c1,1,test
Clc1ccc(Cl)cc1,1,test
C=C(C)C(=O)OCC1CO1,1,test
C=CCN,1,test
C/C=N/O,1,test
CC(C)OC(C)C,0,test
Cc1cc(C)cc(N)c1,1,test
OC1CCCCC1,0,test
CCCOC(C)=O,1,test
CCNCC,0,test
C/C=C/C=C/C(=O)O,1,test
C1CCCCC1,1,test
CCOCCOC(C)=O,1,test
CCCSCCC,1,test
CCCCCCCCN,1,test
CCCCCCCCCCO,1,test
C#CC(C)(C)O,0,test
O=[N+]([O-])c1c(Cl)c(Cl)cc(Cl)c1Cl,1,test
CC(C)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1O,1,test
CCN(CC)c1ccc(C=O)cc1,1,test
Cc1ccc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
CCOC(=O)CC(SP(=S)(OC)OC)C(=O)OCC,1,test
c1ccc(OCC2CO2)cc1,1,test
O=c1ccc(=O)[nH][nH]1,1,test
C1COCCO1,0,test
C=C(C)[C@@H]1[C@H]2OC(=O)[C@@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C.CC(C)(O)C1C2OC(=O)C1C1(O)CC3OC34C(=O)OC2C14C,1,test
Cc1ccc(S(=O)(=O)NCl)cc1,1,test
c1ccc2c(c1)sc1ccccc12,1,test
CCCCC(CC)C(=O)O.CCCCC(CC)C(=O)O.[Zn+2],1,test
O=C(O)CN(CC(=O)O)CC(=O)O,0,test
CC(C)OC(=S)S,1,test
CCNC(=S)OC(C)C,1,test
O=C(O)C(=O)O,0,test
COc1ccc(O)cc1,1,test
C1CN2CCN1CC2,0,test
COP(=S)(OC)Oc1cc(Cl)c(Cl)cc1Cl,1,test
Cl[C@H]1[C@H](Cl)[C@@H](Cl)[C@H](Cl)[C@H](Cl)[C@H]1Cl,1,test
O=[N+]([O-])c1ccc(F)c(Cl)c1,1,test
N#CN,1,test
CCOP(=O)(OCC)OC(=CCl)c1ccc(Cl)cc1Cl,1,test
C1=CC2CCC1C2,1,test
CC(=O)Br,1,test
O=[N+]([O-])c1ccccc1[N+](=O)[O-],1,test
Clc1cccc(Cl)c1,1,test
O=Cc1ccccc1[N+](=O)[O-],1,test
Cc1cccc([N+](=O)[O-])c1N,1,test
CC1CCCCC1C,1,test
CC1CCCC(C)C1,1,test
CCOP(=O)(O)OCC,0,test
O=Cc1c([N+](=O)[O-])cc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
ClCc1ccccc1Cl,1,test
Cc1cc([N+](=O)[O-])ccc1[N+](=O)[O-],1,test
OCCN1CCOCC1,0,test
CCCSSCCC,1,test
O=Cc1cc(Cl)ccc1O,1,test
CN(C)P(=O)(N(C)C)N(C)C,0,test
Cc1ncc[nH]1,0,test
COP(=S)(S)OC,1,test
Clc1ccccc1NNc1ccccc1Cl,1,test
Cc1c[nH]c(-c2ccccc2)n1,1,test
Cc1ccc2nc(C)ccc2c1,1,test
CC(=O)c1cccn1C,0,test
CNC(=O)ON=C1[C@@H](Cl)[C@@H]2C[C@H](C#N)[C@H]1C2,1,test
CCNc1nc(NCC)nc(SC)n1,1,test
CCCCN(CC)C(=O)SCCC,1,test
O=c1ssc(Cl)c1Cl,1,test
CCCCOC(C)COC(=O)COc1ccc(Cl)cc1Cl.CCCCOCC(C)OC(=O)COc1ccc(Cl)cc1Cl,1,test
NC[C@@H]1O[C@H](O[C@H]2[C@@H](O)[C@H](O[C@@H]3[C@@H](O)[C@H](N)C[C@H](N)[C@H]3O[C@H]3O[C@H](CN)[C@@H](O)[C@H](O)[C@H]3N)O[C@@H]2CO)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O.NC[C@H]1O[C@H](O[C@H]2[C@H](O[C@@H]3O[C@H](CO)[C@@H](O[C@H]4O[C@H](CN)[C@@H](O)[C@H](O)[C@H]4N)[C@H]3O)[C@@H](O)[C@H](N)C[C@@H]2N)[C@H](N)[C@@H](O)[C@@H]1O,1,test
C=Cn1c2ccccc2c2ccccc21,1,test
C=CC=CC#N,1,test
Oc1ccc(/N=N/c2ccccc2)cc1,1,test
O=Cc1cc(Br)ccc1O,1,test
N#Cc1cc(Cl)c(O)c(Cl)c1,1,test
Clc1cccc(C(Cl)(Cl)Cl)n1,1,test
CCN(CC)CC.O=C(O)COc1cc(Cl)c(Cl)cc1Cl,1,test
C#CCC(C)O,1,test
CCSC(=O)N1CCCCCC1,1,test
COP(=O)(OC)SC(SP(=O)(OC)OC)c1ccccc1,1,test
CC1(C)CC(O)CC(C)(C)N1,0,test
CCCCCCc1ccc(O)cc1,1,test
O=C(CBr)c1ccc(O)cc1,1,test
O=C(O)COc1ccc(Cl)cc1Cl.OCCN(CCO)CCO,0,test
CC1CCOB(OCCC(C)OB2OCCC(C)O2)O1,1,test
OCCCc1cccnc1,0,test
CCCCC(CC)C(=O)OOC(C)(C)C,1,test
CC(=O)Oc1c([N+](=O)[O-])cc([N+](=O)[O-])cc1C(C)(C)C,1,test
N#Cc1sc2c(=O)c3ccccc3c(=O)c=2sc1C#N,1,test
CC(=O)CCc1ccc(OC(C)=O)cc1,1,test
COP1(=S)OCc2ccccc2O1,1,test
C/C=C/c1ccc(OC)cc1,1,test
COc1cc(OC)c(OC)cc1C=O,1,test
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H](Cl)[C@@H](Cl)C[C@@H]3[C@@]1(Cl)C2(Cl)Cl,1,test
O=C1c2ccccc2C(=O)N1CCCCBr,1,test
COP(=S)(OC)Oc1nc(Cl)c(Cl)cc1Cl,1,test
CCOP(O)(=S)OCC,1,test
CCCCCCCCCCC(C)=O,1,test
Cc1cccc(Cl)c1C#N,1,test
CNC(=O)Oc1ccccc1C1OCCO1,1,test
CCCCCCCCCCCN,1,test
CC1=CC[C@@H]2C[C@H]1C2(C)C,1,test
CC(C(c1ccc(Cl)cc1)c1ccc(Cl)cc1)[N+](=O)[O-].CCC(C(c1ccc(Cl)cc1)c1ccc(Cl)cc1)[N+](=O)[O-],1,test
CCCCOC(C)CO,1,test
N#CC(Br)(Br)C(N)=O,1,test
Clc1cc(Cl)c(-c2cc(Cl)c(Cl)c(Cl)c2)cc1Cl.Clc1ccc(-c2cc(Cl)c(Cl)cc2Cl)cc1Cl.Clc1ccc(-c2ccc(Cl)c(Cl)c2)c(Cl)c1,1,test
CCOP(=S)(Oc1ccc(C#N)cc1)c1ccccc1,1,test
Cl[C@@H]1C2(Cl)[C@H]3C4C=CC3[C@H]3[C@@H]4[C@]1(Cl)C(Cl)(Cl)[C@]32Cl,1,test
Oc1cc(Cl)cc(Cl)c1O,1,test
OC(c1ccc(Cl)cc1)(c1ccc(Cl)cc1)C1CC1,1,test
COC(=O)C(Cl)Cc1ccc(Cl)cc1,1,test
CNC(=O)O/N=C1\[C@@H](Cl)[C@@H]2C[C@H](C#N)[C@H]1C2,1,test
CCc1cccc(CC)c1N(COC)C(=O)CCl,1,test
CNC(=O)ON=C(C)SC,1,test
Clc1c(Cl)c(Cl)c(-c2ccccc2)c(Cl)c1Cl,1,test
C#CC(C)(O)CCC(C)C,1,test
O=CNC(Nc1ccc(Cl)c(Cl)c1)C(Cl)(Cl)Cl,1,test
CCOP(=O)(NC(C)C)Oc1ccc(SC)c(C)c1,1,test
C#CCC1=C(C)C(OC(=O)C2C(C=C(C)C)C2(C)C)CC1=O,1,test
CCOP(=S)(OCC)Oc1ncn(-c2ccccc2)n1,1,test
Oc1c(Cl)c(Cl)cc(Cl)c1Cl.Oc1c(Cl)cc(Cl)c(Cl)c1Cl.Oc1cc(Cl)c(Cl)c(Cl)c1Cl,1,test
CCC(C)c1cccc(OC(=O)N(C)Sc2ccccc2)c1,1,test
CCOC1Oc2ccc(OS(C)(=O)=O)cc2C1(C)C,1,test
COS(=O)(=O)O.C[N+](C)(C)c1ccccc1,0,test
CCN(CC)c1c([N+](=O)[O-])cc(C(F)(F)F)c(N)c1[N+](=O)[O-],1,test
COP(=O)(NC(C)=O)SC,1,test
O=S1OC[C@@H]2[C@H](CO1)[C@]1(Cl)C(Cl)=C(Cl)[C@]2(Cl)C1(Cl)Cl,1,test
COCCCNCc1cc(OC)c(OC)c(OC)c1,0,test
Clc1cc(-c2cc(Cl)c(Cl)c(Cl)c2Cl)c(Cl)c(Cl)c1Cl,1,test
Clc1ccc(Cl)c(-c2cc(Cl)c(Cl)cc2Cl)c1,1,test
CC(C)=CC1C(C(=O)OC(C#N)c2cccc(Oc3ccccc3)c2)C1(C)C,1,test
CCCCCCCCCCC(C)c1ccc(S(=O)(=O)O)cc1,1,test
CC[C@H]1O[C@]2(CC[C@@H]1C)C[C@@H]1C[C@@H](C/C=C(\C)C[C@@H](C)/C=C/C=C3\CO[C@@H]4[C@H](O)C(C)=C[C@@H](C(=O)O1)[C@]34O)O2,1,test
CCCCCCCCCCCCOCC,1,test
OC[P+](CO)(CO)CO,1,test
c1cc(-c2ccsc2)cs1.c1csc(-c2cccs2)c1.c1csc(-c2ccsc2)c1,1,test
Cn1cc(-c2ccccc2)c(=O)c(-c2cccc(C(F)(F)F)c2)c1,1,test
O=C(O)c1cc(Oc2ccc(C(F)(F)F)cc2Cl)ccc1[N+](=O)[O-],1,test
CCCOCCO,1,test
O=C(Nc1ccccc1)Nc1ccnc(Cl)c1,1,test
CCCCOC(=O)C(C)Oc1ccc(Oc2ccc(C(F)(F)F)cn2)cc1,1,test
CCCC(=NOCC)C1=C(O)CC(CC(C)SCC)CC1=O,1,test
O=[N+]([O-])c1cc(C(F)(F)F)c(Cl)c([N+](=O)[O-])c1Nc1ncc(C(F)(F)F)cc1Cl,1,test
Cn1sc2c(c1=O)CCC2,1,test
CCOP(=O)(SC(C)CC)SC(C)CC,1,test
CC(C)[C@@H](Nc1ccc(C(F)(F)F)cc1Cl)C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1,1,test
CCOC(=O)CSc1nc(C(C)(C)C)nn1C(=O)N(C)C,1,test
Cc1ccc(-c2c(Cl)nc(C#N)n2S(=O)(=O)N(C)C)cc1,1,test
CCOC(=O)C(Cl)Cc1cc(-n2nc(C)n(C(F)F)c2=O)c(F)cc1Cl,1,test
CON=C(C(=O)OC)c1ccccc1CON=C(C)c1cccc(C(F)(F)F)c1,1,test
COC(=O)N(C(=O)N1CO[C@@]2(C(=O)OC)Cc3cc(Cl)ccc3C2=N1)c1ccc(OC(F)(F)F)cc1,1,test
Cc1cc(C)c(C2=C(OC(=O)CC(C)(C)C)C3(CCCC3)OC2=O)c(C)c1,1,test
CC(C)(C1CCC(O)CC1)C1CCC(O)CC1,1,test
CCCCCCCCCCCCCCCCCC[N+](C)(C)CCCCCCCCCCCCCCCCCC,1,test
COc1cc2c3c(ccc4c5ccc6c7c(cc(OC)c(c1c34)c75)-c1ccccc1C6=O)C(=O)c1ccccc1-2,0,test
C=C1[C@H]2CC[C@@H]3[C@H]2C(C)(C)CCC[C@]13C,1,test
CC(C)C1=CC2=CC[C@H]3[C@](C)(CO)CCC[C@]3(C)[C@H]2CC1,1,test
O=[N+]([O-])c1ccc2c(N=Nc3ccc4ccccc4c3O)c(O)cc(S(=O)(=O)O)c2c1,1,test
CCCOCC(C)OC(=O)C(C)Oc1cc(Cl)c(Cl)cc1Cl,1,test
c1ccc2oc(-c3ccc(-c4nc5ccccc5o4)s3)nc2c1,0,test
CCCCCCCCCCCC(=O)Oc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl,1,test
CCCCCCCC[N+](C)(CCCCCCCC)CCCCCCCC,1,test
C=C1C(C)(C)C2(Cl)C(Cl)(Cl)C(Cl)(Cl)C1(Cl)C2(Cl)Cl.CC1C(Cl)(Cl)C(Cl)(Cl)C2(Cl)C(C)(C)C1(Cl)C2(Cl)Cl,1,test
Clc1ccc(-c2cc(Cl)c(Cl)cc2Cl)cc1Cl.Clc1ccc(-c2ccc(Cl)c(Cl)c2)c(Cl)c1.Clc1ccc(-c2ccc(Cl)cc2)cc1.Clc1ccc(-c2ccc(Cl)cc2Cl)cc1.Clc1ccc(-c2ccccc2)cc1,1,test
CC(c1ccccc1)c1cc(C(C)c2ccccc2)c(O)c(C(C)c2ccccc2)c1,1,test
CCCCCCCCCCCCCCCCCC[N+](C)(C)CCC[Si](OC)(OC)OC,1,test
Clc1cc(-c2cc(Cl)c(Cl)c(Cl)c2Cl)c(Cl)c(Cl)c1Cl.Clc1cc(Cl)c(-c2cc(Cl)c(Cl)c(Cl)c2Cl)cc1Cl.Clc1cc(Cl)c(-c2cc(Cl)c(Cl)cc2Cl)cc1Cl.Clc1ccc(-c2cc(Cl)c(Cl)cc2Cl)cc1Cl,1,test
CC(C)CCCCCCCCCCNCCCCCCCCCCC(C)C,1,test
CC(C)c1c(Cl)cc2c(c1Cl)CC[C@H]1[C@](C)(C(=O)O)CCC[C@]21C,1,test
CC(Oc1ccc(Oc2ncc(C(F)(F)F)cc2Cl)cc1)C(=O)O,0,test
CC(C)CCCCCCCOP(=O)(Oc1ccccc1)Oc1ccccc1.O=P(Oc1ccccc1)(Oc1ccccc1)Oc1ccccc1,1,test
CS(=O)(=O)c1cc(C(F)(F)F)ccc1C(=O)c1cnoc1C1CC1,1,test
Oc1ccc(N=Nc2ccccc2)cc1,1,test
CCC=CCCO,0,test
C=CCC1=C(C)C(OC(=O)[C@@H]2[C@@H](C=C(C)C)C2(C)C)CC1=O,1,test
C=C[C@@]1(C)CCC2C(=CCC3[C@]2(C)CCC[C@@]3(C)C(=O)O)C1,1,test
C1=C(c2cccnc2)CCNC1,1,test
COc1cc(C(C)=O)ccc1O,1,test
CC1(C)CC[C@]2(C(=O)O)CC[C@]3(C)C(=CC[C@@H]4[C@@]5(C)CC[C@H](OC(=O)/C=C/c6ccc(O)cc6)C(C)(C)[C@@H]5CC[C@]43C)[C@@H]2C1,1,test
C[n+]1ccccc1,1,test
CCN(Cc1ccccc1)c1ccc(N=Nc2ccc(N=Nc3cc(C)c(N=Nc4cccc(S(=O)(=O)O)c4)cc3C)c3cc(S(=O)(=O)O)ccc23)c(C)c1.CCN(Cc1ccccc1)c1ccc(N=Nc2ccc(N=Nc3cc(C)c(N=Nc4cccc(S(=O)(=O)O)c4)cc3C)c3ccc(S(=O)(=O)O)cc23)c(C)c1,1,test
CC1=CC[C@H]2C[C@@H]1C2(C)C,1,test
O=C(CN(Cc1ccccc1)Cc1ccccc1)c1ccc(O)c(O)c1,1,test
OC(Cn1ccnc1)c1ccc(Cl)cc1Cl,1,test
N[C@@H](CSS(=O)(=O)O)C(=O)O,1,test
CC(N)c1ccccc1,1,test
CNC(=O)N(C)c1ncnc2c1ncn2C(=O)NC,1,test
c1ccc(Nc2cccc3ccccc23)cc1,1,test
c1ccc(Cn2cc[n+](Cc3ccccc3)c2)cc1,1,test
Nc1ccccc1F,1,test
CN(C)c1ccc([C+](c2ccccc2)c2ccc(N(C)C)cc2)cc1,1,test
CC(N)=CC#N,1,test
C=CCOCCCCCC,1,test
CC(=O)C=Cc1cccc(Cl)c1,1,test
CCOc1cc(N2CCOCC2)c(OCC)cc1N,1,test
CCOP(=O)(OCC)SCCSCC,1,test
Nc1ccc2c(c1)C(=O)/C(=N/Nc1ccc3c(c1)C(=O)/C(=N/Nc1ccc(N=Nc4ccc(N)c5ccc(S(=O)(=O)O)cc45)cc1)C(S(=O)(=O)O)=C3)C(S(=O)(=O)O)=C2,0,test
C=C(C)[C@@H]1[C@@H]2OC(=O)[C@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C.CC(C)(O)[C@@H]1[C@@H]2OC(=O)[C@H]1[C@]1(O)C[C@H]3O[C@]34C(=O)O[C@H]2[C@]14C,1,test
CSC(C)=NOC(=O)N(C)SN(C)C(=O)O/N=C(\C)SC,1,test
C/C=C/C(=O)Oc1c(CCCCCC(C)C)cc([N+](=O)[O-])cc1[N+](=O)[O-],1,test
CC(Cl)Cl,0,test
CCCC(Cl)CCCC(Cl)CCC(Cl)CCC(Cl)CCC(Cl)CCCC(Cl)CCC,0,test
CC(C)(CC(=O)O)n1cc(-c2cc(F)cc3c2-c2ccccc2C3(O)C(F)(F)F)cn1,0,test
C=C[C@H]1CN2CCC1C[C@H]2[C@H](O)c1ccnc2ccc(OC)cc12,1,test
CCCCCCCCCCCC[N+](C)(C)Cc1ccccc1,1,test
O=C(Nc1ccc2c(c1)C=C(S(=O)(=O)O)/C(=N/Nc1ccc(N=Nc3ccc(S(=O)(=O)O)cc3)cc1)C2=O)c1ccccc1,0,test
CCOP(=S)(OCC)Oc1cc(Cl)c(I)cc1Cl,0,test
CCCc1cc(=O)[nH]c(=S)[nH]1,0,test
CCCC(CC)CCOC(=O)COc1cc(Cl)c(Cl)cc1Cl,1,test
CCNc1nc(Cl)nc(N(CC)CC)n1,1,test
CCCCCCCc1ccc(O)cc1,1,test
CC(C(=O)O)N(C(=O)c1ccccc1)c1ccc(F)c(Cl)c1,1,test
COc1ccc(/C(=C/C(=O)N2CCOCC2)c2ccc(Cl)cc2)cc1OC,1,test
CC(C)(C)N(NC(=O)c1ccc(Cl)cc1)C(=O)c1ccccc1,1,test
C=CCNC(N)=S,0,test
COC(=O)c1ccccc1S(=O)(=O)NC(=O)Nc1nc(C)cc(C)n1,1,test
CCCCn1sc2ccccc2c1=O,1,test
CCN(Cc1cccc(S(=O)(=O)O)c1)c1ccc([C+](c2ccc(N(CC)Cc3cccc(S(=O)(=O)O)c3)cc2)c2ccccc2S(=O)(=O)O)cc1,0,test
CCOc1ccc(N=Nc2ccc(/C=C\c3ccc(N=Nc4ccc(OCC)cc4)cc3S(=O)(=O)O)c(S(=O)(=O)O)c2)cc1,0,test
CC(C)N(CCC(C(N)=O)(c1ccccc1)c1ccccn1)C(C)C,1,test
CC(C)(Oc1ccc(Cl)cc1)C(=O)O,0,test
Nc1ccc(N=Nc2ccccc2)c(N)c1,1,test
CCCC(=O)O,0,test
CN/C(=N/[N+](=O)[O-])NCC1CCOC1,1,test
Nc1cc(Cl)nc(C(=O)O)c1Cl,1,test
CC(N)=NP(=S)(Oc1ccc(Cl)cc1)Oc1ccc(Cl)cc1,1,test
N=C(N)N[N+](=O)[O-],0,test
O=C1C=C(S(=O)(=O)O)c2cc([N+](=O)[O-])ccc2/C1=N\Nc1c(O)ccc2ccccc12.O=C1C=C(S(=O)(=O)O)c2cc([N+](=O)[O-])ccc2/C1=N\Nc1c(O)ccc2ccccc12.O=C1C=C(S(=O)(=O)O)c2cc([N+](=O)[O-])ccc2/C1=N\Nc1c(O)ccc2ccccc12.[Cr].[Cr],1,test
CCCCOC(=O)COC(=O)c1ccccc1C(=O)OCCCC,1,test
CC(C)[C@@]1(O)[C@@H](OC(=O)c2ccc[nH]2)[C@@]2(O)[C@@]3(C)CC4(O)O[C@@]5([C@H](O)[C@@H](C)CC[C@]35O)[C@@]2(O)C41C,1,test
CCOC(=O)COc1ccc(Cl)cc1Cl,1,test
C1CN1P1(N2CC2)=NP(N2CC2)(N2CC2)=NP(N2CC2)(N2CC2)=N1,1,test
CC(Oc1cc(Cl)c(Cl)cc1Cl)C(=O)O.CCN(CC)CC,1,test
CC(C)C[C@H](NC(=O)[C@H](CCCCN)NC(=O)[C@H](CCCN=C(N)N)NC(=O)[C@H](CCCCN)NC(=O)C(C)C)C(=O)N[C@@H](Cc1ccccc1)C(=O)NCC(=O)O,1,test
CCOP(=O)=O.CCOP(=O)=O.CCOP(=O)=O.[Al+3],0,test
CC(C)N(C)S(=O)(=O)NC(=O)c1cc(-n2c(=O)cc(C(F)(F)F)n(C)c2=O)c(F)cc1Cl,0,test
CCN1CCN(c2cc3c(cc2F)c(=O)c(C(=O)O)cn3C2CC2)CC1,1,test
Cc1ccc(C(C)C)c(O)c1Cl,1,test
Fc1ccc(N(F)F)c(F)c1F,1,test
COc1nc(C)nc(NC(=O)NS(=O)(=O)c2ccccc2CCC(F)(F)F)n1,0,test
CCCCC(CC)COC(=O)C(C)O,1,test
CCCCCCCCCCCCOCCOCCOCCOS(=O)(=O)O,1,test
CCCSP(=S)(OCC)Oc1ccc(Cl)cc1Cl,1,test
C/C=C/C=C/CCCCCCCO,1,test
C=C(C)C,0,test
O=[N+]([O-])c1cc([N+](=O)[O-])c2ccc(S(=O)(=O)O)cc2c1O,0,test
CC[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)C(NC(=O)[C@@H]1CCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(CC)CC,0,test
Clc1cccc(-c2cccc(Cl)c2)c1,1,test
COCCCNc1nc(NCCCOC)nc(SC)n1,1,test
CCCCCCCCCCCCCCCCN(C)C,1,test
CCCCN(CCCC)C(=S)S.CCCCN(CCCC)C(=S)S.[Ni+2],1,test
NCCc1ccccc1,1,test
Cc1cc(C(C#N)c2ccc(Cl)cc2)c(Cl)cc1NC(=O)c1cc(I)cc(I)c1O,1,test
CCCCCCCC/C=C\CCCOC(C)=O,0,test
CCC(C(=O)O)C(S)(CC)C(=O)O,1,test
CCCCN(C)C(=O)Nc1ccc(Cl)c(Cl)c1,1,test
O=C(O)CN(CC(=O)O)CC(O)CN(CC(=O)O)CC(=O)O,0,test
COc1cc(Nc2c(C#N)cnc3cc(OCCCN4CCN(C)CC4)c(OC)cc23)c(Cl)cc1Cl,1,test
CC(C)OC(=O)COc1cc(Cl)c(Cl)cc1Cl,1,test
ClC1=C(Cl)[C@]2(Cl)[C@@H]3[C@@H]([C@H]4C=C[C@@H]3C4)[C@@]1(Cl)C2(Cl)Cl,1,test
Cc1cc(Cl)ccc1O[C@H](C)C(=O)O,1,test
Cl[Sn](Cl)(Cl)c1ccccc1,0,test
COP(=S)(NC(C)C)Oc1ccc(C)cc1[N+](=O)[O-],1,test
C=Cc1cccc(CCl)c1,1,test
COc1cc2c(c3oc(=O)c4c(c13)CCC4=O)C1C=COC1O2,1,test
O=C(O)COc1ccc(Cl)cc1.OCCNCCO,0,test
CS(=O)CCCCN=C=S,1,test
c1ccc(Cc2ccccc2)cc1,1,test
CC(O)(P(=O)(O)O)P(=O)(O)O,0,test
O=C1CCCO1,1,test
CC(C)COP(O)(=S)OCC(C)C,1,test
CN(C)CCCCCCN(C)C,1,test
COc1nc(OC)nc(C(=O)c2cccc(F)c2N(C)S(=O)(=O)C(F)F)n1,1,test
CCC(C)Cl,1,test
COc1ccc2cc(C(C)=O)ccc2c1,1,test
CCC(=O)OC1C=CC2C3CCC(C3)C12,1,test
C1CN(SSN2CCOCC2)CCO1,1,test
OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F,1,test
C1=CCOC1,1,test
CCCCCCCC1CCCC(=O)O1,1,test
CC(O)(P(=O)(O)O)P(=O)(O)O.[Na],0,test
CC(C)(C)OOC(=O)c1ccccc1,1,test
CN(C)CCCN(C)C,1,test
Clc1c(Cl)c(Br)c2c(c1Cl)C1=Nc3c4c(Cl)c(Br)c(Cl)c(Br)c4c4n3[Cu]n3c(c5c(Cl)c(Br)c(Cl)c(Br)c5c3=NC3=NC(=N4)c4c(Cl)c(Cl)c(Br)c(Cl)c43)=NC2=N1,1,test
C=CCCCCCC#CC#CCO,0,test
C1=COCCC1,0,test
CCC(C)(O)CCCC(C)C,1,test
C=C(C)C(=O)OCCCCCCCCCCCCCCCCCC,0,test
COc1ccccc1NC(=O)CC(C)=O,0,test
CC(C)=CCC[C@@H](C)CCO,1,test
CC(=O)C=O,0,test
NC[C@@H]1CCC[C@H](CN)C1,0,test
CC(C)c1ccc2ccccc2c1C(C)C,1,test
Cc1ccc(Nc2ccc(Nc3ccc(C)cc3)c3c2C(=O)c2ccccc2C3=O)cc1,1,test
CC(C)c1ccc(C(C)C)cc1,1,test
NC(=O)c1ccccc1Cl,1,test
CCCC(C)C(N)N,0,test
CCO[Si](CC(C)C)(OCC)OCC,1,test
CC(OC(C)(C)CO)C1CCCC(C)(C)C1.CCC(=O)O,1,test
CC(=O)C(N=Nc1ccc(-c2ccc(N=NC(C(C)=O)C(=O)Nc3ccccc3)c(Cl)c2)cc1Cl)C(=O)Nc1ccccc1,0,test
CCCCc1oc2ccc([N+](=O)[O-])cc2c1C(=O)c1ccc(O)cc1,1,test
CCC(=O)OCC(=O)OC(C)C1CCCC(C)(C)C1,1,test
CC(=O)OCCOC(C)C,1,test
CCCCCCCCCCN(C)C,1,test
CCCCCCCCCCCCCCCCCC(=O)O.CCCCCCCCCCCCCCCCCC(=O)O.[Co+2],1,test
S=C(SN1CCOCC1)N1CCOCC1,1,test
CC(C)CCCCCCCCCCO,1,test
CCCCCCCCCC(C)C=O,1,test
CC(C)(C)S,1,test
O=C(CS)OCC(O)CO,1,test
B12B3B4B1C234,1,test
CCCCCCCCCCCCN(C)C,1,test
Fc1ccc(C2(c3ccccc3F)CO2)cc1,1,test
CC(=O)O[Si](OC(C)=O)(OC(C)(C)C)OC(C)(C)C,0,test
COC(=O)CCC(=O)OC,1,test
CC(=O)OC(C)c1ccccc1,1,test
C=CN1CCCCCC1=O,0,test
C=CCOc1nc(OCC=C)nc(OCC=C)n1,1,test
O=P(O)(O)OCC(O)CO,0,test
OCCCCO,1,test
C=CCOC(=O)CCCCC,1,test
CC(C)(C)C(=O)C(Oc1ccc(Cl)cc1)n1ccnc1,1,test
CC(C=O)Cc1ccc(C(C)(C)C)cc1,1,test
N[C@@H](CCC(=O)C[C@@H](CSSC[C@H](NC(=O)CC[C@H](N)C(=O)O)C(=O)NCC(=O)O)C(=O)NCC(=O)O)C(=O)O,0,test
CCOC(=O)c1ccc(O)cc1,1,test
CC(=O)OCCC1=CC[C@H]2C[C@@H]1C2(C)C,1,test
COC(=O)CCc1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,1,test
O=Cc1ccncc1,1,test
C=CC1CCC(C2CCC(c3ccc(C)cc3)CC2)CC1,0,test
CC/C=C/CCCCCCCCCCCCCCOCC,0,test
OC(c1ccccc1)(c1ccccc1)C1CCNCC1,0,test
C=C(Cl)C(=C)Cl,1,test
CCCCCCCCCCCC(=O)N(C)CC(=O)O,0,test
N#CN.[CaH2],0,test
CNC(=O)O/N=C(\C)SC,1,test
ClC1=C(Cl)[C@@]2(Cl)[C@H]3[C@H]([C@H]4C=C[C@@H]3C4)[C@@]1(Cl)C2(Cl)Cl,1,test
CCCCc1c(C)nc(NCC)nc1OS(=O)(=O)N(C)C,1,test
O=C(OCc1ccccc1)c1sc(Cl)nc1C(F)(F)F,1,test
Cc1cc(C)nc(NC(=O)NS(=O)(=O)c2ccccc2C(=O)OC2COC2)n1,0,test
O=C(NC(=O)c1c(F)cccc1F)Nc1ccc(Oc2ccc(C(F)(F)F)cc2Cl)cc1F,1,test
COC(=O)Nc1nc2cc(Sc3ccccc3)ccc2[nH]1,1,test
CCOC(=O)c1nc(C(Cl)(Cl)Cl)n(-c2ccc(Cl)cc2Cl)n1,1,test
C#CCOc1cc(-n2nc(C(C)(C)C)oc2=O)c(Cl)cc1Cl,1,test
CC(C)OC(=O)C(O)(c1ccc(Cl)cc1)c1ccc(Cl)cc1,1,test
CCCN(CCC)C(=O)SCc1ccccc1,1,test
C[C@@H](Oc1ccc(Oc2ncc(Cl)cc2Cl)cc1)C(=O)N1CCCO1,1,test
COC(=O)c1nc(-c2ccc(Cl)c(OC)c2F)cc(N)c1Cl,1,test
CCCCCCCCCCCCN1C[C@@H](C)O[C@H](C)C1,1,test
CCCC(=O)N(CSP(=S)(OC)OC)c1ccccc1Cl,1,test
O=c1c(C(CC(O)c2ccc(-c3ccc(Br)cc3)cc2)c2ccccc2)c(O)oc2ccccc12,1,test
CCS(=O)(=O)Oc1ccc2c(c1)C(C)(C)CO2,1,test
Cc1ccc([C@H](C)NC(=O)C(NC(=O)OC(C)C)C(C)C)cc1,1,test
CNC(=O)Nc1ccc(C(C)C)cc1,1,test
O=c1c(C2CCCc3ccccc32)c(O)oc2ccccc12,1,test
COc1cc(OC)nc(Sc2cccc3c2C(=O)OC3C)n1,1,test
CCNC(=O)[C@@H](C)OC(=O)Nc1ccccc1,1,test
NC(=O)c1ccccc1C(=O)O,1,test
O=C(O)c1cc(C(F)(F)F)[nH]n1,1,test
Clc1ccc(-c2cc(-c3ccccc3)c(-c3ccccc3)s2)cc1,1,test
CN1C(=O)c2ccccc2S1(=O)=O,0,test
COc1cc(OC)nc(NC(=O)NS(=O)(=O)c2c(-c3nnn(C)n3)cnn2C)n1,0,test
CS(=O)(=O)c1ccc(C(=O)C2C(=O)CCCC2=O)c(Cl)c1,0,test
c1coc(CNc2ncnc3nc[nH]c23)c1,0,test
O=C1[C@H]2CC=CC[C@H]2C(=O)N1SC(Cl)(Cl)Cl,1,test
OCC=CCl,1,test
CC1(C)CC/C(=C\c2ccc(Cl)cc2)C1(O)Cn1cncn1,1,test
Cc1ccc(N=c2sccn2C)c(C)c1,1,test
O=[N+]([O-])NC1=NCCN1Cc1ccc(Cl)nc1,1,test
Nc1nc(N)nc(N)n1,0,test
ClC1=C(Cl)[C@@]2(Cl)[C@H]3C[C@@H](Cl)[C@H](Cl)[C@H]3[C@@]1(Cl)C2(Cl)Cl,1,test
O=C/C=C/c1ccccc1,1,test
C=CC=CCC1=CC(OC(=O)C2C(C=C(C)C)C2(C)C)C(C)C1=O,1,test
ClC1C2(Cl)C=CC1(Cl)C(Cl)(Cl)C2(Cl)Cl,1,test
CCCCCCCCCCCCCCCOCC,1,test
C[P+]1CCCC[P+](C)(C)C1,1,test
C/C=C/[C@H]1[C@H](C(=O)OCc2c(F)c(F)c(C)c(F)c2F)C1(C)C,1,test
CC(C)=NOCCOC(=O)C(C)Oc1ccc(Oc2cnc3cc(Cl)ccc3n2)cc1,1,test
CCOC(=O)CCN(Cc1ccccc1)SN(C)C(=O)O/N=C(/C)SC,1,test
COP(=O)(OC)OC(=CCl)c1ccc(Cl)cc1Cl,1,test
CC(Oc1ccc(Cl)cc1Cl)C(=O)NC(C)(C#N)C(C)C,1,test
CC/C=C\CCCCCCCCOC(C)=O,1,test
CC1=CC[C@H](C(C)C)CC1,1,test
COc1ccc(/C(=C\C(=O)N2CCOCC2)c2ccc(F)cc2)cc1OC,1,test
CCCC(C)(C)C(=O)Nc1ccc(Cl)cc1,1,test
O=C(NC(=O)c1c(F)cccc1F)Nc1cc(Cl)c(Oc2ncc(C(F)(F)F)cc2Cl)c(Cl)c1,0,test
CCCCS/C(=N/c1cccnc1)SCc1ccc(C(C)(C)C)cc1,0,test
O=C1C(Cl)=C(Cl)C(=O)N1c1ccc(F)cc1,1,test
CC(C)(NC(=O)NCc1ccccc1Cl)c1ccccc1,1,test
C(=CCOCc1ccccc1)COCc1ccccc1,1,test
C=CC(=O)OCCCCCCCCCCOC(=O)C=C,1,test
C=C(C)C(=O)OCCOC(=O)C(=C)C,1,test
CC(C)(C)C1CCC2(CC1)OCC(CCl)O2,1,test
C=CC(=O)OCCCOCC(C)(C)COCCCOC(=O)C=C,1,test
CC(=O)OCC=C(C)CCC=C(C)C,1,test
CC1CCC(C(C)C)C(OC(=O)OCCO)C1,1,test
CC(C)CNCC(O)C(Cc1ccccc1)NC(=O)OC(C)(C)C,1,test
CC(C)c1ccc2c(c1)CCC1C(C)(C(=O)O)CCCC21C,1,test
C=CC(=O)OCCC(C)CCOC(=O)C=C,1,test
C=CC(C)Cl,1,test
CC1(C)C(C=C(Br)Br)C1C(=O)OC(C#N)c1cccc(Oc2ccccc2)c1,1,test
C#CC1(O)CCC2C3CCc4cc(O)ccc4C3CCC21C,1,test
CC(C)=CC1CC(C)CCO1,1,test
CC=C(C=O)CC,1,test
C=CC(C)(O)CCC=C(C)CC,1,test
CCC1OC2(CCC1C)CC1CC(CC=C(C)CC(C)C=CC=C3COC4C(O)C(C)=CC(C(=O)O1)C34O)O2,1,test
CCCC=CC=O,1,test
CCCCCCC1CO1,1,test
CCCCCCCCC=O,1,test
CCCCCCCCCCCCCCCCCC[N+](C)(C)CC(O)CCl,1,test
CCCCCCCCN(C)C,1,test
CCCCOC(=O)C=CC(=O)O,1,test
CCNC(=O)NC(=O)C(C#N)=NOC,1,test
CCOC(=O)CNC(=O)C1CC(C)CCC1C(C)C,1,test
CCOP(=O)(C(=O)c1c(C)cc(C)cc1C)c1ccccc1,1,test
CN(C)C1C(O)=C(C(N)=O)C(=O)C2(O)C(O)=C3C(=O)c4c(O)cccc4C(C)(O)C3C(O)C12,1,test
CNC(=O)C(=NOC)c1ccccc1COc1cc(C)ccc1C,1,test
COC(=O)C=C(C)N,1,test
COC1(C)CCC23CC1C(C)(C)C2CCC3C,1,test
COc1c(O)c(Br)cc(C=O)c1[N+](=O)[O-],1,test
COc1ccc(CC(C)C=O)cc1,1,test
CP(=O)(O)O,1,test
Cc1cc(C)c(Cl)c(O)c1,1,test
Cc1ccc([N+](=O)[O-])cc1C,1,test
Cc1coc2ccc(O)cc12,1,test
Clc1ccc(Oc2cccc(Cl)c2)cc1,1,test
O=Cc1ccc(F)cc1,1,test
NCC1OC(OC2C(CO)OC(OC3C(O)C(N)CC(N)C3OC3OC(CN)C(O)C(O)C3N)C2O)C(N)C(O)C1O,1,test
Nc1ccc(CCNCC(O)c2ccccc2)cc1,1,test
N#CC1SC2=C(SC1C#N)C(=O)c1ccccc1C2=O,1,test
N#CO,1,test
OC1CCc2[nH]c3ccccc3c2C1,1,test
OCCOc1ccc2ccccc2c1,1,test
Oc1cc(Cl)cc(Br)c1,1,test
SCCSCC(CS)SCCS,1,test
