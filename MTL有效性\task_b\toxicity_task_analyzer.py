"""
毒性任务间相似性分析器

专门分析不同毒性预测任务之间的TFP-G参数相似性
"""

import torch
import numpy as np
import os
import sys
sys.path.append('..')
from core import ParameterExtractor, SimilarityCalculator, Visualizer
import warnings
warnings.filterwarnings('ignore')


class ToxicityTaskAnalyzer:
    """毒性任务间相似性分析器"""
    
    def __init__(self, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        self.extractor = ParameterExtractor(device)
        self.calculator = SimilarityCalculator()
        self.visualizer = Visualizer('task_b_results')

        # 模型名称映射（用于图表显示）
        self.display_name_mapping = {
            'FishLC50': 'FishAT',
            'FishEL_NOEC': 'FishCT',
            'DMRepNOEC': 'DMCT',
            'DMImbEC50': 'DMAT',
            'AlaGroErC50': 'AlgAT'
        }

        # 预定义模型路径
        self.model_paths = {
            # 多任务模型
            'MTL-scr': 'model/MTL-scr_early_stop.pth',
            # 单任务模型
            'FishLC50': 'model/STL-scr-FishLC50_early_stop.pth',
            'FishEL_NOEC': 'model/STL-scr-FishEL_NOEC_early_stop.pth',
            'DMRepNOEC': 'model/STL-scr-DMRepNOEC_early_stop.pth',
            'DMImbEC50': 'model/STL-scr-DMImbEC50_early_stop.pth',
            'AlaGroErC50': 'model/STL-scr-AlaGroErC50_early_stop.pth'
        }
        
        # 预定义模型配置
        self.model_configs = {
            # 多任务模型配置
            'MTL-scr': {
                'in_feats': 40,
                'rgcn_hidden_feats': [128, 128],
                'n_tasks': 5,  # 多任务模型
                'classifier_hidden_feats': 128,
                'rgcn_drop_out': 0.4,
                'dropout': 0.3,
                'loop': True,
                'return_mol_embedding': False,
                'return_weight': False
            },
            # 单任务模型配置
            'FishLC50': {
                'in_feats': 40,
                'rgcn_hidden_feats': [128, 128],
                'n_tasks': 1,  # 单任务模型
                'classifier_hidden_feats': 128,
                'rgcn_drop_out': 0.3,
                'dropout': 0.3,
                'loop': True,
                'return_mol_embedding': False,
                'return_weight': False
            },
            'FishEL_NOEC': {
                'in_feats': 40,
                'rgcn_hidden_feats': [64, 128],  
                'n_tasks': 1,  # 单任务模型
                'classifier_hidden_feats': 64,  
                'rgcn_drop_out': 0.3,
                'dropout': 0.3,
                'loop': True,
                'return_mol_embedding': False,
                'return_weight': False
            },
            'DMRepNOEC': {
                'in_feats': 40,
                'rgcn_hidden_feats': [128, 128],
                'n_tasks': 1,  # 单任务模型
                'classifier_hidden_feats': 64,
                'rgcn_drop_out': 0.3,
                'dropout': 0.3,
                'loop': True,
                'return_mol_embedding': False,
                'return_weight': False
            },
            'DMImbEC50': {
                'in_feats': 40,
                'rgcn_hidden_feats': [128, 128],
                'n_tasks': 1,  # 单任务模型
                'classifier_hidden_feats': 64,
                'rgcn_drop_out': 0.3,
                'dropout': 0.3,
                'loop': True,
                'return_mol_embedding': False,
                'return_weight': False
            },
            'AlaGroErC50': {
                'in_feats': 40,
                'rgcn_hidden_feats': [128, 128],
                'n_tasks': 1,  # 单任务模型
                'classifier_hidden_feats': 128,
                'rgcn_drop_out': 0.4,
                'dropout': 0.3,
                'loop': True,
                'return_mol_embedding': False,
                'return_weight': False
            }
        }

    def _convert_names_for_display(self, task_names):
        """将技术名称转换为显示名称"""
        return [self.display_name_mapping.get(name, name) for name in task_names]

    def analyze_task_similarity(self, custom_model_paths=None, custom_configs=None):
        """
        分析不同毒性任务之间的TFP-G参数相似性
        
        Args:
            custom_model_paths (dict): 自定义模型路径
            custom_configs (dict): 自定义模型配置
        
        Returns:
            dict: 分析结果
        """
        print("开始任务B：毒性任务间相似性分析")
        print("-" * 50)
        
        # 使用自定义路径或默认路径
        model_paths = custom_model_paths if custom_model_paths else self.model_paths
        model_configs = custom_configs if custom_configs else self.model_configs
        
        # 加载所有任务的模型
        all_tfp_params = {}
        all_attention_params = {}
        all_fc_params = {}
        valid_tasks = []
        
        for task_name, model_path in model_paths.items():
            # 在任务B中排除MTL模型，只分析单任务模型
            if task_name == 'MTL-scr':
                print(f"跳过MTL模型: {task_name}")
                continue

            if os.path.exists(model_path):
                try:
                    print(f"开始加载模型: {task_name}")

                    # 获取对应的配置，如果没有找到则使用FishLC50作为默认单任务配置
                    config = model_configs.get(task_name, model_configs['FishLC50'])
                    print(f"  使用配置: GNN={config['rgcn_hidden_feats']}, FC={config['classifier_hidden_feats']}")

                    # 加载模型
                    print(f"  正在加载模型文件...")
                    model = self.extractor.load_model_from_checkpoint(model_path, config)
                    print(f"  ✅ 模型加载成功")

                    # 提取不同类型的参数
                    print(f"  正在提取TFP参数...")
                    tfp_params = self.extractor.extract_tfp_parameters(model, task_name)
                    print(f"  ✅ TFP参数提取成功，参数数量: {len(tfp_params) if isinstance(tfp_params, dict) else 'N/A'}")

                    # 添加参数统计信息
                    if isinstance(tfp_params, dict):
                        total_tfp_params = sum(len(param) for param in tfp_params.values())
                        param_stats = {}
                        for key, param in tfp_params.items():
                            param_stats[key] = {
                                'shape': param.shape if hasattr(param, 'shape') else len(param),
                                'mean': float(np.mean(param)),
                                'std': float(np.std(param)),
                                'min': float(np.min(param)),
                                'max': float(np.max(param))
                            }
                        print(f"    总TFP参数数: {total_tfp_params:,}")
                        print(f"    参数统计示例: {list(param_stats.keys())[:3]}")

                        # 显示前几个参数的统计信息
                        for i, (key, stats) in enumerate(list(param_stats.items())[:2]):
                            print(f"      {key}: 形状={stats['shape']}, 均值={stats['mean']:.6f}, 标准差={stats['std']:.6f}")

                    print(f"  正在提取注意力参数...")
                    attention_params = self.extractor.extract_attention_parameters(model, task_name)
                    print(f"  ✅ 注意力参数提取成功，参数数量: {len(attention_params) if isinstance(attention_params, dict) else 'N/A'}")

                    print(f"  正在提取FC参数...")
                    fc_params = self.extractor.extract_fc_parameters(model, task_name)
                    print(f"  ✅ FC参数提取成功，参数数量: {len(fc_params) if isinstance(fc_params, dict) else 'N/A'}")

                    # 详细检查FC参数内容
                    if isinstance(fc_params, dict):
                        if fc_params:
                            total_params = sum(len(param) for param in fc_params.values())
                            print(f"    FC参数详情: {list(fc_params.keys())}")
                            print(f"    总参数数量: {total_params}")
                            for key, param in list(fc_params.items())[:2]:  # 只显示前2个
                                print(f"    {key}: shape={param.shape if hasattr(param, 'shape') else len(param)}, 均值={np.mean(param):.6f}")
                        else:
                            print(f"    ❌ FC参数字典为空！")
                    else:
                        print(f"    ❌ FC参数不是字典类型: {type(fc_params)}")

                    all_tfp_params[task_name] = tfp_params
                    all_attention_params[task_name] = attention_params
                    all_fc_params[task_name] = fc_params
                    valid_tasks.append(task_name)

                    print(f"✅ 模型 {task_name} 完全加载成功")

                except Exception as e:
                    print(f"❌ 加载模型失败 {task_name}: {str(e)}")
                    import traceback
                    print(f"详细错误信息:")
                    traceback.print_exc()
            else:
                print(f"❌ 模型文件不存在: {model_path}")
        
        if len(valid_tasks) < 2:
            raise ValueError("需要至少2个有效模型进行相似性分析")
        
        print(f"成功加载 {len(valid_tasks)} 个单任务模型: {valid_tasks}")

        # 直接使用所有成功加载的单任务模型，不进行配置过滤
        analysis_tasks = valid_tasks

        print(f"将分析所有 {len(analysis_tasks)} 个单任务模型: {analysis_tasks}")

        # 使用所有加载成功的模型参数
        filtered_tfp_params = all_tfp_params
        filtered_attention_params = all_attention_params
        filtered_fc_params = all_fc_params

        # 分析整体TFP-G参数相似性
        overall_results = self._analyze_overall_similarity(filtered_tfp_params, analysis_tasks)

        # 分析注意力权重相似性
        attention_results = self._analyze_attention_similarity(filtered_attention_params, analysis_tasks)

        # 分析FC层参数相似性
        fc_results = self._analyze_fc_similarity(filtered_fc_params, analysis_tasks)
        
        print(f"\n任务B分析完成！结果保存在 task_b_results/ 目录")
        
        return {
            'overall': overall_results,
            'attention': attention_results,
            'fc': fc_results,
            'valid_tasks': valid_tasks
        }

    def _extract_common_parameters(self, all_params):
        """提取所有模型的参数，处理维度不匹配问题"""
        if not all_params:
            return {}

        print(f"处理 {len(all_params)} 个模型的参数")

        # 为每个模型提取所有参数并转换为向量
        processed_params = {}

        for task_name, params in all_params.items():
            # 将所有参数连接成一个向量
            param_vector = np.concatenate([param for param in params.values()])
            processed_params[task_name] = param_vector
            print(f"{task_name}: 总参数维度 {len(param_vector)}")

        # 检查维度是否一致
        dimensions = [len(params) for params in processed_params.values()]
        unique_dims = set(dimensions)

        if len(unique_dims) == 1:
            print(f"✅ 所有模型参数维度一致: {list(unique_dims)[0]}")
            return processed_params
        else:
            print(f"⚠️  模型参数维度不一致: {unique_dims}")
            print("使用零填充策略对齐维度...")

            # 找到最大维度，用零填充较小的向量
            max_dim = max(dimensions)
            print(f"将所有参数填充到最大维度: {max_dim}")

            aligned_params = {}
            for task_name, param_vector in processed_params.items():
                current_dim = len(param_vector)

                # 添加参数统计信息
                param_mean = np.mean(param_vector)
                param_std = np.std(param_vector)
                param_min = np.min(param_vector)
                param_max = np.max(param_vector)

                print(f"{task_name} 原始参数统计:")
                print(f"  维度: {current_dim:,}")
                print(f"  均值: {param_mean:.6f}, 标准差: {param_std:.6f}")
                print(f"  范围: [{param_min:.6f}, {param_max:.6f}]")

                if current_dim < max_dim:
                    # 用零填充到最大维度
                    padding_size = max_dim - current_dim
                    padded_vector = np.concatenate([param_vector, np.zeros(padding_size)])
                    aligned_params[task_name] = padded_vector

                    # 填充后统计
                    padded_mean = np.mean(padded_vector)
                    padded_std = np.std(padded_vector)
                    padding_ratio = padding_size / max_dim

                    print(f"  填充: {padding_size:,} 个零 ({padding_ratio:.2%})")
                    print(f"  填充后: 均值={padded_mean:.6f}, 标准差={padded_std:.6f}")
                else:
                    aligned_params[task_name] = param_vector
                    print(f"  无需填充")

                print()  # 空行分隔

            return aligned_params

    def _group_models_by_gnn_config(self, model_names):
        """按GNN配置对模型分组"""
        groups = {}

        for model_name in model_names:
            config = self.model_configs.get(model_name, self.model_configs['FishLC50'])
            gnn_config_key = str(config['rgcn_hidden_feats'])

            if gnn_config_key not in groups:
                groups[gnn_config_key] = []
            groups[gnn_config_key].append(model_name)

        print("GNN配置分组:")
        for config_key, models in groups.items():
            print(f"  {config_key}: {models}")

        return groups

    def _process_mtl_parameters(self, all_tfp_params, all_attention_params, all_fc_params, valid_tasks):
        """处理多任务模型参数，为每个单任务创建对应的MTL参数"""
        processed_tfp = {}
        processed_attention = {}
        processed_fc = {}

        # 单任务名称列表
        single_task_names = ['FishLC50', 'FishEL_NOEC', 'DMRepNOEC', 'DMImbEC50', 'AlaGroErC50']

        # 首先复制所有单任务模型的参数
        for task_name in valid_tasks:
            if task_name != 'MTL-scr':
                processed_tfp[task_name] = all_tfp_params[task_name]
                processed_attention[task_name] = all_attention_params[task_name]
                processed_fc[task_name] = all_fc_params[task_name]

        # 如果有MTL模型，为每个单任务创建对应的MTL参数
        if 'MTL-scr' in valid_tasks:
            print("处理MTL模型参数，为每个单任务创建对应的MTL参数...")

            # 重新加载MTL模型以提取任务特定参数
            mtl_model_path = self.model_paths['MTL-scr']
            mtl_config = self.model_configs['MTL-scr']

            if os.path.exists(mtl_model_path):
                try:
                    mtl_model = self.extractor.load_model_from_checkpoint(mtl_model_path, mtl_config)

                    # 为每个单任务提取对应的MTL参数
                    for task_name in single_task_names:
                        if task_name in valid_tasks:  # 只处理实际存在的单任务模型
                            mtl_task_key = f"MTL-{task_name}"

                            # 提取MTL模型中该任务的特定参数
                            mtl_task_params = self.extractor.extract_task_specific_parameters(
                                mtl_model, 'MTL-scr', task_name
                            )

                            processed_tfp[mtl_task_key] = mtl_task_params

                            # 注意力和FC参数也需要相应处理
                            # 这里简化处理，使用相同的参数
                            processed_attention[mtl_task_key] = {}
                            processed_fc[mtl_task_key] = {}

                            # 提取注意力参数
                            for key, value in mtl_task_params.items():
                                if 'attention' in key:
                                    processed_attention[mtl_task_key][key] = value
                                elif ('fc' in key or 'output' in key):
                                    processed_fc[mtl_task_key][key] = value

                            print(f"  创建 {mtl_task_key} 参数")

                except Exception as e:
                    print(f"❌ 处理MTL参数失败: {str(e)}")

        return processed_tfp, processed_attention, processed_fc

    def _analyze_overall_similarity(self, all_params, valid_tasks):
        """分析整体TFP-G参数相似性"""
        print("\n分析整体TFP-G参数相似性...")

        # 提取共同的参数类型（GNN和注意力参数）进行比较
        common_params = self._extract_common_parameters(all_params)

        # 计算相似性矩阵
        similarity_matrix, task_names = self.calculator.compute_similarity_matrix(common_params)
        
        # 生成热力图（使用显示名称）
        display_names = self._convert_names_for_display(task_names)
        self.visualizer.create_heatmap(
            similarity_matrix,
            display_names,
            'TFP-G Parameter Similarity: Different Toxicity Tasks',
            'overall_task_similarity_heatmap.png'
            # 使用默认统一尺寸 (12, 10)
        )
        
        # 保存相似性矩阵（使用显示名称）
        self.visualizer.save_similarity_matrix(
            similarity_matrix,
            display_names,
            'overall_task_similarity_matrix.csv'
        )
        
        # 计算统计信息
        stats = self.calculator.get_similarity_statistics(similarity_matrix)
        similarities_list = self.calculator.compute_pairwise_similarities(common_params)
        
        # 生成分析报告
        self.visualizer.generate_analysis_report(
            similarity_matrix,
            task_names,
            similarities_list,
            stats,
            'overall_task_analysis_report.txt',
            "任务B：整体TFP-G参数相似性分析"
        )
        
        return {
            'similarity_matrix': similarity_matrix,
            'task_names': task_names,
            'stats': stats,
            'similarities_list': similarities_list
        }
    
    def _analyze_attention_similarity(self, attention_params, valid_tasks):
        """分析注意力权重相似性"""
        print("分析注意力权重相似性...")

        # 处理注意力参数维度不匹配问题
        processed_attention_params = {}
        attention_dimensions = []

        for task_name, params in attention_params.items():
            if isinstance(params, dict) and params:
                param_vector = np.concatenate([param for param in params.values()])
            elif isinstance(params, np.ndarray):
                param_vector = params
            else:
                # 如果没有注意力参数，创建一个零向量
                param_vector = np.array([0.0])

            processed_attention_params[task_name] = param_vector
            attention_dimensions.append(len(param_vector))
            print(f"  {task_name}: 注意力参数维度 {len(param_vector)}")

        # 使用零填充处理维度不一致问题
        if len(set(attention_dimensions)) > 1:
            print(f"注意力参数维度不一致，使用零填充对齐...")

            max_dim = max(attention_dimensions)
            print(f"填充到最大维度: {max_dim}")

            filtered_attention_params = {}
            for task_name, param_vector in processed_attention_params.items():
                current_dim = len(param_vector)
                if current_dim < max_dim:
                    padding_size = max_dim - current_dim
                    padded_vector = np.concatenate([param_vector, np.zeros(padding_size)])
                    filtered_attention_params[task_name] = padded_vector
                    print(f"  {task_name}: 填充 {padding_size} 个零")
                else:
                    filtered_attention_params[task_name] = param_vector
                    print(f"  {task_name}: 无需填充")
        else:
            filtered_attention_params = processed_attention_params

        # 计算相似性矩阵
        similarity_matrix, task_names = self.calculator.compute_similarity_matrix(filtered_attention_params)
        
        # 生成热力图（使用显示名称）
        display_names = self._convert_names_for_display(task_names)
        self.visualizer.create_heatmap(
            similarity_matrix,
            display_names,
            'Attention Weights Similarity: Different Toxicity Tasks',
            'attention_similarity_heatmap.png'
            # 使用默认统一尺寸 (12, 10)
        )
        
        # 保存相似性矩阵（使用显示名称）
        self.visualizer.save_similarity_matrix(
            similarity_matrix,
            display_names,
            'attention_similarity_matrix.csv'
        )
        
        return {
            'similarity_matrix': similarity_matrix,
            'task_names': task_names
        }
    
    def _analyze_fc_similarity(self, fc_params, valid_tasks):
        """分析FC层参数相似性"""
        print("分析FC层参数相似性...")

        # 处理FC参数维度不匹配问题
        processed_fc_params = {}
        fc_dimensions = []

        for task_name, params in fc_params.items():
            print(f"\n  调试 {task_name} FC参数:")
            print(f"    参数类型: {type(params)}")
            print(f"    参数内容: {params if not isinstance(params, dict) or len(params) < 5 else f'dict with {len(params)} keys'}")

            if isinstance(params, dict) and params:
                param_vector = np.concatenate([param for param in params.values()])
                print(f"    提取到 {len(params)} 个FC层参数")
            elif isinstance(params, np.ndarray):
                param_vector = params
                print(f"    直接使用numpy数组")
            else:
                # 如果没有FC参数，创建一个零向量
                param_vector = np.array([0.0])
                print(f"    ❌ 没有FC参数，使用零向量！")

            processed_fc_params[task_name] = param_vector
            fc_dimensions.append(len(param_vector))
            print(f"    最终维度: {len(param_vector)}")
            if len(param_vector) > 0:
                print(f"    参数统计: 均值={np.mean(param_vector):.6f}, 标准差={np.std(param_vector):.6f}")
                print(f"    前5个值: {param_vector[:5]}")
                print(f"    后5个值: {param_vector[-5:]}")
            else:
                print(f"    ❌ 参数向量为空！")

        # 添加详细的调试信息
        print(f"\nFC参数维度详情:")
        for task_name, param_vector in processed_fc_params.items():
            print(f"  {task_name}: {len(param_vector)}维")
            if len(param_vector) > 0:
                print(f"    均值: {np.mean(param_vector):.6f}")
                print(f"    标准差: {np.std(param_vector):.6f}")
                print(f"    前5个值: {param_vector[:5]}")

        # 使用截断方法处理维度不一致问题
        if len(set(fc_dimensions)) > 1:
            print(f"\nFC参数维度不一致，使用截断对齐...")
            print(f"维度范围: {min(fc_dimensions)} - {max(fc_dimensions)}")

            min_dim = min(fc_dimensions)
            print(f"截断到最小维度: {min_dim}")

            filtered_fc_params = {}
            for task_name, param_vector in processed_fc_params.items():
                current_dim = len(param_vector)
                if current_dim > min_dim:
                    # 截断到最小维度
                    truncated_vector = param_vector[:min_dim]
                    filtered_fc_params[task_name] = truncated_vector
                    print(f"  {task_name}: 从{current_dim}维截断到{min_dim}维")
                else:
                    filtered_fc_params[task_name] = param_vector
                    print(f"  {task_name}: 保持{current_dim}维不变")
        else:
            print(f"\n所有FC参数维度相同: {fc_dimensions[0]}，无需截断")
            filtered_fc_params = processed_fc_params

        # 强制重新计算相似性矩阵（避免缓存问题）
        print(f"\n开始计算FC层相似性矩阵...")
        print(f"参与计算的任务: {list(filtered_fc_params.keys())}")
        for task_name, param_vector in filtered_fc_params.items():
            print(f"  {task_name}: {len(param_vector)}维, 均值={np.mean(param_vector):.6f}, 标准差={np.std(param_vector):.6f}")

        similarity_matrix, task_names = self.calculator.compute_similarity_matrix(filtered_fc_params)
        print(f"✅ 相似性矩阵计算完成: {similarity_matrix.shape}")
        
        # 生成热力图（使用显示名称）
        display_names = self._convert_names_for_display(task_names)
        self.visualizer.create_heatmap(
            similarity_matrix,
            display_names,
            'FC Layers Parameter Similarity: Different Toxicity Tasks',
            'fc_similarity_heatmap.png'
            # 使用默认统一尺寸 (12, 10)
        )
        
        # 保存相似性矩阵（使用显示名称）
        self.visualizer.save_similarity_matrix(
            similarity_matrix,
            display_names,
            'fc_similarity_matrix.csv'
        )
        
        return {
            'similarity_matrix': similarity_matrix,
            'task_names': task_names
        }
    
    def print_similarity_results(self, results):
        """打印相似性分析结果"""
        overall_results = results['overall']
        similarity_matrix = overall_results['similarity_matrix']
        task_names = overall_results['task_names']

        # 分类任务
        multi_task_models = []
        single_task_models = []

        for task_name in task_names:
            if task_name in self.model_configs:
                if self.model_configs[task_name]['n_tasks'] > 1:
                    multi_task_models.append(task_name)
                else:
                    single_task_models.append(task_name)

        print("\n=" * 50)
        print("毒性任务相似性分析结果")
        print("=" * 50)

        print(f"\n多任务模型: {multi_task_models}")
        print(f"单任务模型: {single_task_models}")

        print(f"\n所有任务对相似性结果:")
        for i, name1 in enumerate(task_names):
            for j, name2 in enumerate(task_names):
                if i < j:
                    # 标识模型类型
                    type1 = "多任务" if name1 in multi_task_models else "单任务"
                    type2 = "多任务" if name2 in multi_task_models else "单任务"
                    print(f"{name1}({type1}) vs {name2}({type2}): {similarity_matrix[i, j]:.4f}")

        # 分析多任务与单任务模型间的相似性
        if multi_task_models and single_task_models:
            print(f"\n多任务与单任务模型间相似性:")
            multi_single_similarities = []
            for multi_task in multi_task_models:
                for single_task in single_task_models:
                    i = task_names.index(multi_task)
                    j = task_names.index(single_task)
                    similarity = similarity_matrix[i, j]
                    multi_single_similarities.append(similarity)
                    print(f"  {multi_task} vs {single_task}: {similarity:.4f}")

            if multi_single_similarities:
                avg_similarity = np.mean(multi_single_similarities)
                print(f"\n多任务与单任务模型平均相似性: {avg_similarity:.4f}")

        # 分析单任务模型间的相似性
        if len(single_task_models) > 1:
            print(f"\n单任务模型间相似性:")
            single_similarities = []
            for i, task1 in enumerate(single_task_models):
                for j, task2 in enumerate(single_task_models):
                    if i < j:
                        idx1 = task_names.index(task1)
                        idx2 = task_names.index(task2)
                        similarity = similarity_matrix[idx1, idx2]
                        single_similarities.append(similarity)
                        print(f"  {task1} vs {task2}: {similarity:.4f}")

            if single_similarities:
                avg_similarity = np.mean(single_similarities)
                print(f"\n单任务模型间平均相似性: {avg_similarity:.4f}")
