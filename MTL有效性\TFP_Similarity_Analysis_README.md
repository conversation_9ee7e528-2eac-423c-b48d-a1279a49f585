# TFP-G参数相似性分析工具

本工具包用于分析MGA模型中TFP-G（毒性指纹发生器）组件的参数相似性，实现两个主要分析任务：

- **任务A**: 不同FC层数的MGA与具有3个FC层的原始MGA的TFP-G参数相似性
- **任务B**: 不同毒性任务之间的TFP-G参数相似性

## 文件结构

```
code/
├── tfp_similarity_analysis.py          # 核心分析类
├── run_tfp_similarity_analysis.py      # 主要执行脚本
├── generate_similarity_heatmaps.py     # 生成发表级热力图
├── real_model_similarity_analysis.py   # 真实模型参数分析
└── TFP_Similarity_Analysis_README.md   # 本说明文件
```

## 核心组件说明

### 1. TFP-G组件结构

根据代码分析，MGA模型的TFP-G包含以下组件：

- **深度毒性提取器(DTE)**: RGCN层提取分子特征
- **注意力机制**: WeightAndSum类中的atom_weighting_specific
- **全连接层**: fc_layers1, fc_layers2, fc_layers3
- **输出层**: output_layer1

### 2. 参数提取策略

```python
# 注意力权重参数
attention_params = {
    "attention_task_i_weight": ...,  # 任务特定注意力权重
    "attention_task_i_bias": ...,    # 任务特定注意力偏置
    "shared_attention_weight": ...,  # 共享注意力权重
    "shared_attention_bias": ...     # 共享注意力偏置
}

# 全连接层参数
fc_params = {
    "fc1_task_i_weight": ...,        # FC层1权重
    "fc1_task_i_bias": ...,          # FC层1偏置
    "fc2_task_i_weight": ...,        # FC层2权重
    "fc2_task_i_bias": ...,          # FC层2偏置
    "fc3_task_i_weight": ...,        # FC层3权重
    "fc3_task_i_bias": ...,          # FC层3偏置
    "output_task_i_weight": ...,     # 输出层权重
    "output_task_i_bias": ...        # 输出层偏置
}
```

## 使用方法

### 方法1: 使用主要执行脚本

```bash
cd code
python run_tfp_similarity_analysis.py
```

这将执行完整的分析流程，包括：
- 任务A：不同FC层配置的相似性分析
- 任务B：不同毒性任务的相似性分析
- 注意力权重专门分析

### 方法2: 使用真实模型分析

```bash
cd code
python real_model_similarity_analysis.py
```

这将从实际的模型checkpoint文件中提取参数进行分析。

### 方法3: 生成发表级热力图

```bash
cd code
python generate_similarity_heatmaps.py
```

这将生成类似您提供图片的高质量热力图。

## 输出结果

### 任务A输出
- `fc_layer_similarity_heatmap.png`: FC层配置相似性热力图
- `fc_layer_similarity_matrix.csv`: 相似性数值矩阵

### 任务B输出
- `task_similarity_heatmap.png`: 毒性任务相似性热力图
- `attention_similarity_heatmap.png`: 注意力权重相似性热力图
- `task_similarity_matrix.csv`: 任务相似性数值矩阵

### 真实模型分析输出
- `tfp_overall_similarity.png`: 整体TFP-G参数相似性
- `attention_similarity.png`: 注意力权重相似性
- `fc_similarity.png`: FC层参数相似性
- 对应的CSV文件

## 相似性计算方法

支持三种相似性计算方法：

1. **余弦相似性** (默认)
   ```python
   similarity = cosine_similarity([vec1], [vec2])[0, 0]
   ```

2. **皮尔逊相关系数**
   ```python
   similarity = np.corrcoef(vec1, vec2)[0, 1]
   ```

3. **欧几里得距离**
   ```python
   similarity = 1 / (1 + np.linalg.norm(vec1 - vec2))
   ```

## 模型配置

### 标准配置
```python
base_config = {
    'in_feats': 40,
    'rgcn_hidden_feats': [128, 128],
    'n_tasks': 5,  # 根据具体任务调整
    'classifier_hidden_feats': 128,
    'rgcn_drop_out': 0.2,
    'dropout': 0.2,
    'loop': True,
    'return_mol_embedding': False,
    'return_weight': False
}
```

### FC层配置变体
```python
fc_configs = {
    '1_layer_FC': {'num_layers': 1, 'hidden_dims': [128]},
    '2_layer_FC': {'num_layers': 2, 'hidden_dims': [128, 128]},
    '4_layer_FC': {'num_layers': 4, 'hidden_dims': [128, 128, 128, 64]},
    '5_layer_FC': {'num_layers': 5, 'hidden_dims': [128, 128, 128, 64, 32]}
}
```

## 注意事项

1. **模型文件路径**: 确保模型文件存在于指定路径
2. **GPU内存**: 大型模型可能需要足够的GPU内存
3. **参数匹配**: 不同任务的模型参数结构需要兼容
4. **结果解释**: 相似性值范围[0,1]，1表示完全相似，0表示完全不同

## 自定义分析

### 添加新的相似性度量
```python
def custom_similarity(params1, params2):
    # 实现自定义相似性计算
    pass

# 在compute_parameter_similarity中添加新方法
elif method == 'custom':
    similarity = custom_similarity(vec1, vec2)
```

### 添加新的参数类型
```python
def extract_custom_parameters(self, model, model_name):
    custom_params = {}
    # 提取特定的参数类型
    for name, param in model.named_parameters():
        if 'custom_component' in name:
            custom_params[name] = param.detach().cpu().numpy().flatten()
    return custom_params
```

## 故障排除

1. **模型加载失败**: 检查模型文件路径和格式
2. **参数不匹配**: 确保模型配置正确
3. **内存不足**: 减少批处理大小或使用CPU
4. **图片显示问题**: 检查matplotlib后端设置

## 扩展功能

- 支持更多相似性度量方法
- 添加统计显著性检验
- 实现参数重要性分析
- 支持动态参数变化追踪
