"""
MTL有效性分析主执行脚本

运行所有TFP-G参数相似性分析任务
"""

import os
import sys
import matplotlib.pyplot as plt
import argparse

# 添加路径以导入模块
sys.path.append('.')
from task_a.fc_layer_analyzer import FCLayerAnalyzer
from task_b.toxicity_task_analyzer import ToxicityTaskAnalyzer


def run_task_a():
    """运行任务A：FC层配置相似性分析"""
    print("=" * 60)
    print("任务A：FC层配置相似性分析")
    print("=" * 60)
    
    # 创建分析器
    analyzer = FCLayerAnalyzer()
    
    # 基础模型路径
    base_model_path = '../model/MTL-scr_early_stop.pth'
    
    # 检查模型文件是否存在
    if not os.path.exists(base_model_path):
        print(f"错误：基础模型文件不存在: {base_model_path}")
        print("请确保模型文件路径正确")
        return False
    
    try:
        # 获取默认配置
        base_config = analyzer.get_default_base_config()
        
        # 执行分析
        similarity_matrix, model_names = analyzer.analyze_fc_layer_similarity(
            base_model_path, base_config
        )
        
        print("\n任务A分析完成！")
        return True
        
    except Exception as e:
        print(f"任务A执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def run_task_b():
    """运行任务B：毒性任务间相似性分析"""
    print("=" * 60)
    print("任务B：毒性任务间相似性分析")
    print("=" * 60)
    
    # 创建分析器
    analyzer = ToxicityTaskAnalyzer()
    
    # 检查模型文件是否存在
    missing_files = []
    updated_model_paths = {}
    
    for task_name, model_path in analyzer.model_paths.items():
        full_path = f"../{model_path}"
        updated_model_paths[task_name] = full_path
        if not os.path.exists(full_path):
            missing_files.append(f"{task_name}: {full_path}")
    
    if missing_files:
        print("警告：以下模型文件不存在:")
        for missing_file in missing_files:
            print(f"  - {missing_file}")
        print("将只分析存在的模型文件")
    
    try:
        # 执行分析
        results = analyzer.analyze_task_similarity(
            custom_model_paths=updated_model_paths
        )
        
        # 打印结果
        analyzer.print_similarity_results(results)
        
        print("\n任务B分析完成！")
        return True
        
    except Exception as e:
        print(f"任务B执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def run_custom_analysis():
    """自定义运行分析"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    print("MTL有效性分析工具")
    print("=" * 60)
    print("本工具实现两个核心分析任务：")
    print("任务A：FC层配置相似性分析")
    print("  - 分析不同FC层数配置的MGA模型与原始3层FC配置的TFP-G参数相似性")
    print("  - 对比1层、2层、4层、5层FC配置与基准模型的相似程度")
    print("任务B：毒性任务间相似性分析")
    print("  - 分析不同毒性预测任务之间的TFP-G参数相似性")
    print("  - 包括多任务模型(MTL-scr)和单任务模型(FishLC50、FishEL_NOEC、DMRepNOEC、DMImbEC50、AlaGroErC50)")
    print("  - 对比多任务学习与单任务学习在参数共享方面的差异")
    print("=" * 60)

    # 用户选择要运行的任务
    print("\n请选择要运行的任务:")
    print("1. 任务A - FC层配置相似性分析")
    print("2. 任务B - 毒性任务间相似性分析")
    print("3. 运行所有任务")
    print("4. 退出")

    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()

            if choice == '1':
                print("\n开始运行任务A...")
                success = run_task_a()
                if success:
                    print("任务A完成！")
                break

            elif choice == '2':
                print("\n开始运行任务B...")
                success = run_task_b()
                if success:
                    print("任务B完成！")
                break

            elif choice == '3':
                print("\n开始运行所有任务...")
                success_a = run_task_a()
                success_b = run_task_b()
                print(f"\n任务完成情况: 任务A {'成功' if success_a else '失败'}, 任务B {'成功' if success_b else '失败'}")
                break

            elif choice == '4':
                print("退出程序")
                return

            else:
                print("无效选择，请输入1-4之间的数字")

        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            return
        except Exception as e:
            print(f"输入错误: {e}")

def main():
    """主函数 - 保持命令行兼容性"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description='MTL有效性分析')
        parser.add_argument('--task', choices=['a', 'b', 'all'], default='all',
                           help='选择要运行的任务: a (FC层配置分析), b (毒性任务分析), all (所有任务)')

        args = parser.parse_args()

        print("MTL有效性分析工具")
        print("=" * 60)

        success_count = 0
        total_tasks = 0

        if args.task in ['a', 'all']:
            total_tasks += 1
            if run_task_a():
                success_count += 1

        if args.task in ['b', 'all']:
            total_tasks += 1
            if run_task_b():
                success_count += 1
    
        # 总结
        print("\n" + "=" * 60)
        print("分析总结")
        print("=" * 60)
        print(f"成功完成: {success_count}/{total_tasks} 个任务")

        if success_count > 0:
            print("\n结果文件位置:")
            if args.task in ['a', 'all']:
                print("任务A结果:")
                print("  - task_a_results/fc_layer_similarity_heatmap.png")
                print("  - task_a_results/fc_layer_similarity_matrix.csv")
                print("  - task_a_results/fc_layer_analysis_report.txt")

            if args.task in ['b', 'all']:
                print("任务B结果:")
                print("  - task_b_results/overall_task_similarity_heatmap.png")
                print("  - task_b_results/attention_similarity_heatmap.png")
                print("  - task_b_results/fc_similarity_heatmap.png")
                print("  - task_b_results/overall_task_similarity_matrix.csv")
                print("  - task_b_results/attention_similarity_matrix.csv")
                print("  - task_b_results/fc_similarity_matrix.csv")
                print("  - task_b_results/overall_task_analysis_report.txt")

        print("=" * 60)
    else:
        # 没有命令行参数，使用交互式模式
        run_custom_analysis()


if __name__ == "__main__":
    main()
