FishLC50:{'max_depth': 8, 'min_child_samples': 50, 'reg_lambda': 0.1, 'reg_alpha': 0.1, 'learning_rate': 0.05, 'n_estimators': 220, 'feature_fraction': 0.8, 'bagging_fraction': 0.75, 'num_leaves': 101}
FishEL_NOEC:{'max_depth': 9, 'min_child_samples': 30, 'reg_lambda': 1, 'reg_alpha': 0.01, 'learning_rate': 0.05, 'n_estimators': 280, 'feature_fraction': 0.85, 'bagging_fraction': 0.85, 'num_leaves': 121}
DMRepNOEC:{'max_depth': 9, 'min_child_samples': 40, 'reg_lambda': 0.1, 'reg_alpha': 0.01, 'learning_rate': 0.05, 'n_estimators': 160, 'feature_fraction': 0.85, 'bagging_fraction': 0.75, 'num_leaves': 51}
DMImbEC50:{'max_depth': 4, 'min_child_samples': 50, 'reg_lambda': 0.1, 'reg_alpha': 0.1, 'learning_rate': 0.05, 'n_estimators': 200, 'feature_fraction': 0.85, 'bagging_fraction': 0.75, 'num_leaves': 91}
AlaGroErC50:{'max_depth': 9, 'min_child_samples': 60, 'reg_lambda': 1e-05, 'reg_alpha': 0.01, 'learning_rate': 0.005, 'n_estimators': 240, 'feature_fraction': 0.85, 'bagging_fraction': 0.85, 'num_leaves': 51}
