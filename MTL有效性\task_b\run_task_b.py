"""
任务B执行脚本

运行毒性任务间相似性分析
"""

import os
import sys
import matplotlib.pyplot as plt

# 添加路径以导入模块
sys.path.append('..')
sys.path.append('../..')

from toxicity_task_analyzer import ToxicityTaskAnalyzer


def main():
    """主函数"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    print("=" * 60)
    print("任务B：毒性任务间相似性分析")
    print("=" * 60)
    
    # 创建分析器
    analyzer = ToxicityTaskAnalyzer()
    
    # 检查模型文件是否存在
    missing_files = []
    for task_name, model_path in analyzer.model_paths.items():
        full_path = f"../../{model_path}"
        if not os.path.exists(full_path):
            missing_files.append(f"{task_name}: {full_path}")
    
    if missing_files:
        print("警告：以下模型文件不存在:")
        for missing_file in missing_files:
            print(f"  - {missing_file}")
        print("将只分析存在的模型文件")
    
    try:
        # 更新模型路径为相对于当前位置的路径
        updated_model_paths = {}
        for task_name, model_path in analyzer.model_paths.items():
            updated_model_paths[task_name] = f"../../{model_path}"
        
        # 执行分析
        results = analyzer.analyze_task_similarity(
            custom_model_paths=updated_model_paths
        )
        
        # 打印结果
        analyzer.print_similarity_results(results)
        
        print("\n" + "=" * 60)
        print("任务B分析完成！")
        print("结果文件:")
        print("- task_b_results/overall_task_similarity_heatmap.png")
        print("- task_b_results/attention_similarity_heatmap.png")
        print("- task_b_results/fc_similarity_heatmap.png")
        print("- task_b_results/overall_task_similarity_matrix.csv")
        print("- task_b_results/attention_similarity_matrix.csv")
        print("- task_b_results/fc_similarity_matrix.csv")
        print("- task_b_results/overall_task_analysis_report.txt")
        print("=" * 60)
        
    except Exception as e:
        print(f"任务B执行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
