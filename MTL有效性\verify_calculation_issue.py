#!/usr/bin/env python3
"""
验证计算问题的脚本
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

def simulate_problem_scenario():
    """模拟当前问题场景"""
    print("=" * 60)
    print("模拟当前FC相似性计算问题")
    print("=" * 60)
    
    # 模拟当前观察到的问题：
    # 1. FishAT vs FishCT: 0.107 (太低)
    # 2. FishCT vs DMCT: 0.988 (太高)
    # 3. 很多值在0.1左右
    
    print("当前观察到的问题:")
    print("- FishAT vs FishCT: 0.107 (应该很高，都是鱼类)")
    print("- FishCT vs DMCT: 0.988 (不合理，跨物种相似性太高)")
    print("- 大多数相似性在0.1左右")
    
    # 这种模式很像零填充的结果
    print(f"\n分析：这种模式很像零填充方法的结果")
    
    # 模拟零填充场景
    print(f"\n模拟零填充场景:")
    
    # FishAT: 128维参数
    fish_at_real = np.random.normal(0.1, 0.05, 128)
    
    # FishCT: 64维参数 + 64个零
    fish_ct_real = np.random.normal(0.12, 0.06, 64)
    fish_ct_padded = np.concatenate([fish_ct_real, np.zeros(64)])
    
    # DMCT: 64维参数 + 64个零  
    dm_ct_real = np.random.normal(-0.05, 0.08, 64)
    dm_ct_padded = np.concatenate([dm_ct_real, np.zeros(64)])
    
    # DMAT: 64维参数 + 64个零
    dm_at_real = np.random.normal(-0.04, 0.11, 64)
    dm_at_padded = np.concatenate([dm_at_real, np.zeros(64)])
    
    print(f"FishAT (真实128维): 均值={np.mean(fish_at_real):.4f}")
    print(f"FishCT (64维+零填充): 均值={np.mean(fish_ct_padded):.4f}")
    print(f"DMCT (64维+零填充): 均值={np.mean(dm_ct_padded):.4f}")
    print(f"DMAT (64维+零填充): 均值={np.mean(dm_at_padded):.4f}")
    
    # 计算零填充方法的相似性
    params_matrix = np.array([fish_at_real, fish_ct_padded, dm_ct_padded, dm_at_padded])
    similarity_matrix = cosine_similarity(params_matrix)
    
    labels = ['FishAT', 'FishCT', 'DMCT', 'DMAT']
    
    print(f"\n零填充方法相似性矩阵:")
    print("        ", "  ".join(f"{label:>8}" for label in labels))
    for i, label in enumerate(labels):
        row = "  ".join(f"{similarity_matrix[i][j]:8.4f}" for j in range(len(labels)))
        print(f"{label:>8}: {row}")
    
    # 分析结果
    fish_at_ct_sim = similarity_matrix[0][1]
    fish_ct_dm_ct_sim = similarity_matrix[1][2]
    
    print(f"\n零填充结果分析:")
    print(f"FishAT vs FishCT: {fish_at_ct_sim:.4f}")
    print(f"FishCT vs DMCT: {fish_ct_dm_ct_sim:.4f}")
    
    if fish_at_ct_sim < 0.2 and fish_ct_dm_ct_sim > 0.9:
        print("✅ 这与观察到的问题模式一致！")
        print("   零填充导致了不合理的相似性结果")
    
    return similarity_matrix

def simulate_correct_truncation():
    """模拟正确的截断方法"""
    print(f"\n" + "=" * 60)
    print("模拟正确的截断方法")
    print("=" * 60)
    
    # 使用相同的基础数据，但应用截断方法
    np.random.seed(42)  # 固定随机种子
    
    # FishAT: 128维参数，但只取前64维
    fish_at_full = np.random.normal(0.1, 0.05, 128)
    fish_at_truncated = fish_at_full[:64]
    
    # FishCT: 64维参数（与FishAT前64维相似）
    fish_ct_params = fish_at_truncated + np.random.normal(0, 0.01, 64)
    
    # DMCT: 64维参数（不同分布）
    dm_ct_params = np.random.normal(-0.05, 0.08, 64)
    
    # DMAT: 64维参数（与DMCT相似）
    dm_at_params = dm_ct_params + np.random.normal(0, 0.02, 64)
    
    print(f"FishAT (截断到64维): 均值={np.mean(fish_at_truncated):.4f}")
    print(f"FishCT (64维): 均值={np.mean(fish_ct_params):.4f}")
    print(f"DMCT (64维): 均值={np.mean(dm_ct_params):.4f}")
    print(f"DMAT (64维): 均值={np.mean(dm_at_params):.4f}")
    
    # 计算截断方法的相似性
    params_matrix = np.array([fish_at_truncated, fish_ct_params, dm_ct_params, dm_at_params])
    similarity_matrix = cosine_similarity(params_matrix)
    
    labels = ['FishAT', 'FishCT', 'DMCT', 'DMAT']
    
    print(f"\n截断方法相似性矩阵:")
    print("        ", "  ".join(f"{label:>8}" for label in labels))
    for i, label in enumerate(labels):
        row = "  ".join(f"{similarity_matrix[i][j]:8.4f}" for j in range(len(labels)))
        print(f"{label:>8}: {row}")
    
    # 分析结果
    fish_at_ct_sim = similarity_matrix[0][1]
    fish_ct_dm_ct_sim = similarity_matrix[1][2]
    
    print(f"\n截断方法结果分析:")
    print(f"FishAT vs FishCT: {fish_at_ct_sim:.4f}")
    print(f"FishCT vs DMCT: {fish_ct_dm_ct_sim:.4f}")
    
    if fish_at_ct_sim > fish_ct_dm_ct_sim:
        print("✅ 截断方法产生了合理的结果！")
        print("   鱼类任务间相似性 > 跨物种相似性")

def main():
    """主函数"""
    print("验证FC相似性计算问题...")
    
    simulate_problem_scenario()
    simulate_correct_truncation()
    
    print(f"\n" + "=" * 60)
    print("结论:")
    print("1. 当前结果很可能是零填充方法的结果")
    print("2. 需要重新运行任务B以应用截断方法")
    print("3. 截断方法应该能产生更合理的结果")
    print("=" * 60)

if __name__ == "__main__":
    main()
