"""
任务A执行脚本

运行FC层配置相似性分析
"""

import os
import sys
import matplotlib.pyplot as plt

# 添加路径以导入模块
sys.path.append('..')
sys.path.append('../..')

from fc_layer_analyzer import FCLayerAnalyzer


def main():
    """主函数"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    print("=" * 60)
    print("任务A：FC层配置相似性分析")
    print("=" * 60)
    
    # 创建分析器
    analyzer = FCLayerAnalyzer()
    
    # 基础模型路径（相对于项目根目录）
    base_model_path = '../../model/MTL-scr_early_stop.pth'
    
    # 检查模型文件是否存在
    if not os.path.exists(base_model_path):
        print(f"错误：基础模型文件不存在: {base_model_path}")
        print("请确保模型文件路径正确")
        return
    
    try:
        # 获取默认配置
        base_config = analyzer.get_default_base_config()
        
        # 执行分析
        similarity_matrix, model_names = analyzer.analyze_fc_layer_similarity(
            base_model_path, base_config
        )
        
        print("\n" + "=" * 60)
        print("任务A分析完成！")
        print("结果文件:")
        print("- task_a_results/fc_layer_similarity_heatmap.png")
        print("- task_a_results/fc_layer_similarity_matrix.csv")
        print("- task_a_results/fc_layer_analysis_report.txt")
        print("=" * 60)
        
    except Exception as e:
        print(f"任务A执行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
