FishLC50:{'max_depth': 18, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_features': None, 'n_estimators': 250, 'bootstrap': True, 'max_samples': 1.0}
FishEL_NOEC:{'max_depth': 8, 'min_samples_split': 9, 'min_samples_leaf': 1, 'max_features': None, 'n_estimators': 400, 'bootstrap': True, 'max_samples': 0.7}
DMRepNOEC:{'max_depth': 16, 'min_samples_split': 2, 'min_samples_leaf': 3, 'max_features': None, 'n_estimators': 150, 'bootstrap': True, 'max_samples': 1.0}
DMImbEC50:{'max_depth': 4, 'min_samples_split': 5, 'min_samples_leaf': 2, 'max_features': None, 'n_estimators': 250, 'bootstrap': True, 'max_samples': 0.7}
AlaGroErC50:{'max_depth': 9, 'min_samples_split': 4, 'min_samples_leaf': 3, 'max_features': 'sqrt', 'n_estimators': 200, 'bootstrap': False, 'max_samples': 1.0}
