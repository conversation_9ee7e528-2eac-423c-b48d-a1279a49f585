# 自适应多任务平衡损失函数 (AMTBL) 使用指南

## 🎯 创新点概述

本损失函数创新性地解决了两个关键问题：
1. **正负类样本不平衡问题**：使用改进的Focal Loss机制
2. **多任务数据量不平衡问题**：动态任务权重自适应调整

## 🔧 核心技术特性

### 1. 动态类别平衡 (Focal Loss Enhancement)
- **α参数**：控制正负样本的相对重要性
- **γ参数**：控制对难样本的聚焦程度
- **自适应机制**：根据训练进度动态调整参数

### 2. 任务重要性自适应
- **数据量感知**：样本少的任务获得更高权重
- **学习难度感知**：学习困难的任务获得更多关注
- **动量更新**：使用指数移动平均平滑权重变化

### 3. 温度调节机制
- **初始阶段**：高温度允许权重大幅变化
- **训练后期**：低温度精细化权重分配
- **自适应衰减**：根据训练进度自动调整

### 4. 梯度平衡
- **梯度裁剪**：防止某个任务的梯度过大
- **权重归一化**：确保总权重保持稳定
- **数值稳定性**：避免除零和数值溢出

## 📊 使用方法

### 基础使用
```python
from utils.adaptive_loss import AdaptiveMultiTaskBalancedLoss

# 初始化损失函数
adaptive_loss_fn = AdaptiveMultiTaskBalancedLoss(
    classification_num=5,        # 分类任务数量
    regression_num=0,           # 回归任务数量
    alpha=0.3,                  # Focal Loss类别平衡参数
    gamma=2.5,                  # Focal Loss难样本聚焦参数
    beta=0.999,                 # 任务权重更新动量
    temperature_init=2.0,       # 初始温度
    temperature_decay=0.99,     # 温度衰减率
    device='cuda'
)

# 在训练循环中使用
total_loss, loss_info = adaptive_loss_fn(logits, targets, mask, epoch)
```

### 参数调优建议

#### 对于严重不平衡数据集：
```python
args['adaptive_loss'] = {
    'alpha': 0.4,           # 增加对少数类的关注
    'gamma': 3.0,           # 强化难样本聚焦
    'beta': 0.99,           # 更快的权重适应
    'temperature_init': 3.0, # 更高初始温度
    'temperature_decay': 0.98
}
```

#### 对于相对平衡数据集：
```python
args['adaptive_loss'] = {
    'alpha': 0.25,          # 标准设置
    'gamma': 2.0,           # 标准设置
    'beta': 0.9999,         # 更平滑的权重更新
    'temperature_init': 1.0,
    'temperature_decay': 0.995
}
```

## 📈 性能监控

### 实时监控指标
- **任务损失演化**：观察各任务学习进度
- **任务权重变化**：了解模型对不同任务的关注度
- **温度参数变化**：监控权重调整的精细程度
- **梯度统计**：确保训练稳定性

### 分析工具
```python
from utils.loss_analysis import analyze_dataset_imbalance, visualize_loss_evolution

# 数据集分析
dataset_analysis = analyze_dataset_imbalance(train_set, classification_num, task_names)

# 可视化训练过程
visualize_loss_evolution(loss_analysis_results, save_dir='analysis')
```

## 🔬 实验对比

### 运行对比实验
1. **基线模型**：运行 `Toxicity_MGA_ST.py` 或 `Toxicity_MGA_MT.py`
2. **自适应模型**：运行 `Toxicity_MGA_ST_Adaptive.py` 或 `Toxicity_MGA_MT_Adaptive.py`
3. **结果比较**：使用 `utils/loss_analysis.py` 中的比较工具

### 预期改进
- **类别不平衡任务**：AUC提升 2-5%
- **数据稀少任务**：性能提升 3-8%
- **整体稳定性**：减少训练波动
- **收敛速度**：加快 10-20%

## 🛠️ 故障排除

### 常见问题
1. **内存不足**：减少batch_size或降低temperature_init
2. **训练不稳定**：增加gradient_clip值或调整beta参数
3. **收敛过慢**：增加temperature_decay或调整学习率

### 调试技巧
```python
# 打印详细损失信息
if epoch % 10 == 0:
    stats = adaptive_loss_fn.get_task_statistics()
    print(f"任务权重: {stats['task_weights']}")
    print(f"任务难度: {stats['task_difficulty']}")
    print(f"当前温度: {stats['temperature']}")
```

## 📚 理论基础

### Focal Loss
- **原理**：通过调整损失权重，让模型更关注难分类样本
- **公式**：FL(pt) = -α(1-pt)^γ log(pt)
- **优势**：自动处理类别不平衡，无需手动调整权重

### 动态任务权重
- **数据量权重**：w_data = N_total / N_task
- **难度权重**：w_diff = softmax(loss_history / temperature)
- **组合权重**：w_final = λ * w_diff + (1-λ) * w_data

### 温度调节
- **高温阶段**：允许权重大幅变化，快速适应
- **低温阶段**：精细调整，稳定训练
- **衰减策略**：T(t) = T(0) * decay^t

## 🎨 可视化分析

运行后会自动生成：
- **损失演化图**：展示训练过程中各项指标变化
- **权重分布图**：显示任务权重的动态调整
- **性能对比图**：与基线方法的性能比较
- **详细分析报告**：包含所有统计信息

## 🚀 快速开始

1. **单任务实验**：
   ```bash
   python Toxicity_MGA_ST_Adaptive.py
   ```

2. **多任务实验**：
   ```bash
   python Toxicity_MGA_MT_Adaptive.py
   ```

3. **查看分析结果**：
   ```bash
   # 查看生成的图表
   ls analysis/adaptive_loss/
   
   # 查看详细报告
   cat analysis/adaptive_loss_report.txt
   ```

## 📝 引用和扩展

这个损失函数结合了以下先进技术：
- Focal Loss for Dense Object Detection (Lin et al., 2017)
- Multi-Task Learning with Dynamic Task Weighting
- Temperature Scaling for Calibration
- Gradient-based Task Balancing

可以进一步扩展：
- 添加不确定性估计
- 集成元学习机制
- 引入对抗训练
- 支持在线学习
