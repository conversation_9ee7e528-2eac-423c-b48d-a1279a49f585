﻿smiles,AlgAT,group
C1CCC(NC2CCCCC2)CC1,1,test
COc1ccc(CCC(C)=O)cc1,1,test
CCCC(C)=O,1,test
CCCCCCCN,1,test
O=C(C(Cl)(Cl)Cl)C(Cl)(Cl)Cl,1,test
CCCC=O,1,test
CC(C)Nc1nc(Cl)nc(NC(C)C)n1,1,test
CCCCCCCCCCCC(=O)c1ccccc1,1,test
CCCCCCCCCCN,1,test
C=CC(=O)OCCN(C)C,1,test
C[C@@H](Cl)C(=O)O,1,test
CCCCCCCCCCCC(=O)N(CC)CC,1,test
O=C(O)C(F)(F)C(F)(F)C(F)(F)F,1,test
COC(=O)c1cc(Oc2ccc(Cl)cc2Cl)ccc1[N+](=O)[O-],1,test
O=C(O)Cc1cc(O)ccc1O,1,test
CC(=O)C1C[C@H]2C=C[C@@H]1C2,1,test
CC(=O)Nc1ccc2c(c1)Cc1ccccc1-2,1,test
C[N+](C)(C)CCCCCC[N+](C)(C)C,1,test
Cc1cnc(O)nc1N,1,test
O=CN(c1ccccc1)c1ccccc1,1,test
O=C(O)c1ccc([N+](=O)[O-])cc1,1,test
CN(C)[C@@H]1C(O)=C(C(N)=O)C(=O)[C@@]2(O)C(O)=C3C(=O)c4c(O)cccc4[C@@](C)(O)[C@H]3C[C@@H]12,1,test
C[C@@H](Cc1ccc(C(C)(C)C)cc1)CN1CCCCC1,1,test
CC[N+](CC)(CC)CC,1,test
COC(=O)c1sccc1S(=O)(=O)NC(=O)Nc1nc(C)nc(OC)n1,1,test
Nc1ccccc1[N+](=O)[O-],1,test
COc1nc(C)nc(NC(=O)NS(=O)(=O)c2ccccc2CCC(F)(F)F)n1,1,test
C=CCOC(=O)C(=C)C,1,test
CN(C)N,1,test
CC=O,1,test
C=CC(=O)O,1,test
c1ccc2c(c1)ccc1ccccc12,1,test
Cc1ccc(C(C)(C)C)c(O)c1,1,test
c1ccc(Nc2cccc3ccccc23)cc1,1,test
Oc1ccc(-c2ccccc2)cc1,1,test
c1ccc(C2CO2)cc1,1,test
FC(F)(F)c1ccccc1,1,test
Cc1ccc(C(C)C)cc1,1,test
C=Cc1ccccn1,1,test
COc1ccc(N)cc1,1,test
Nc1ccc(N)cc1,1,test
Brc1ccccc1,1,test
CCCCCS,1,test
CCCCCCCCCCCC(=O)OC,1,test
Cc1c(Cl)cccc1Cl,1,test
CC(=O)C=Cc1ccccc1,1,test
C=C(C)C#N,1,test
CCCCOCCCC,1,test
O=[N+]([O-])c1ccc(F)c(Cl)c1,1,test
O=[N+]([O-])c1ccccc1[N+](=O)[O-],1,test
Nc1cccc(O)c1,1,test
Nc1c(Cl)cc(Cl)cc1Cl,1,test
C=Cn1c2ccccc2c2ccccc21,1,test
Nc1cccc2c(N)cccc12,1,test
C1=CCC2CC=CC2C1,1,test
C=C(C)C(=O)O,1,test
CC(C)(C)c1cc(C(=O)O)c(O)c(C(C)(C)C)c1,1,test
O=C(c1ccc(O)cc1)c1ccc(O)c(O)c1O,1,test
Brc1ccccc1-c1ccccc1Br,1,test
c1ccc(C2=NCCN2)cc1,1,test
C=CCNCC=C,1,test
C=C(C)C(=O)OC(C)(C)C,1,test
C=C(C)C(=O)OCCOC(=O)c1c(CC(O)CCl)cccc1C(=O)O,1,test
CC(=O)OC1CC2CC1C1CCCC21,1,test
CC(=O)Oc1ccccc1[N+](=O)[O-],1,test
CC(C)(C)C(=O)OCCc1ccccc1,1,test
CC(C)(C)c1cc(C(C)(C)C)c(O)c(C(C)(C)C)c1,1,test
C=CCOC(=O)C(C)(C)OC(=O)c1cc([N+](=O)[O-])ccc1Cl,1,test
CC(C)=CCCC(C)=CCCC=O,1,test
CC(C)C(=O)c1ccccc1,1,test
CC(C)C1CCC(O)CC1,1,test
CC(C)CCC1CCC(O)CC1,1,test
CC(C)CCOC(=S)S,1,test
CC(=O)OC(C)CC(C)(C)c1ccccc1,1,test
C=CC(C)CCCC(C)(C)OC(C)=O,1,test
CC(CO)CC(C)c1ccccc1,1,test
CC(N)c1ccccc1,1,test
CC(O)COc1ccc(C(C)(C)c2ccc(OCC(C)O)cc2)cc1,1,test
C#CC1(O)CCC2C3CCC4=CC(=O)CCC4C3CCC21C,1,test
CC1C(C)(C)C2CCc3cncnc3C2C1(C)C,1,test
CC1CCC2(O)C(C)(C)C3CCC2(C)C1C3,1,test
CC=CC(=O)C1CCC(C)(C)C=C1C,1,test
CCC(=O)OCc1ccccc1,1,test
C=CC(C)(O)CCC=C(C)CC,1,test
CCC1(CC=C2CCCc3cc(OC)ccc32)C(=O)CCC1=O,1,test
C=C(C)COCCC=CCC,1,test
CCCCC(CC)C(=O)OCC(COC(=O)C(CC)CCCC)(COC(=O)C(CC)CCCC)COC(=O)C(CC)CCCC,1,test
CCCCCC(=O)OCC,1,test
CCCCCCCC1CCC(=O)O1,1,test
CCCCCCCCC=CCCCCCCCCNCCCN,1,test
CCCCCCCCCCCC(=O)N(C)C,1,test
CCCCCCCCCCCCCCCCCCCCCC(=O)NCCCN(C)C,1,test
CCCCCCCCCCCC[N+](C)(C)C,1,test
CCCCCCCC[N+](C)(C)[O-],1,test
CCCCN(C)C,1,test
CCCCOC(=O)CCP1(=O)Oc2ccccc2-c2ccccc21,1,test
C=C(C)C(=O)OCCOCCOCCCC,1,test
CCCOC(C)OCCc1ccccc1,1,test
CCN(CC)c1ccc(C=CC2=[N+](C)c3ccccc3C2(C)C)cc1,1,test
CCNC(=O)C1CC(C)CCC1C(C)C,1,test
CCOC(=O)C1CCC(C)CC1=O,1,test
CCOC(=O)c1ccc(O)c(O)c1,1,test
C=COCC,1,test
CCSSCC,1,test
CN(C)C(=O)Nc1ccc(Cl)c(Cl)c1,1,test
CN(C)CCCN(CCCN(C)C)CCCN(C)C,1,test
CN(C)c1ccc(C(O)(c2ccc(N(C)C)cc2)c2ccc(N(C)C)cc2)cc1,1,test
CNC(C)C(O)c1ccccc1,1,test
COC(=O)C(C)(C)c1ccc(Br)cc1,1,test
COC(=O)Cc1ccccc1,1,test
COC(Cc1ccccc1)OC,1,test
COCCCN,1,test
COc1cc([N+](=O)[O-])c(OC)cc1Cl,1,test
COc1ccc(OC)cc1,1,test
CS(=O)(=O)OCC1COC(Cn2ccnc2)(c2ccc(Cl)cc2Cl)O1,1,test
Cc1c(Br)c(Br)c(Br)c(Br)c1Br,1,test
Cc1cc([N+](=O)[O-])ccc1N,1,test
Cc1ccc(N)cc1O,1,test
Cc1ccc2cc(C)ccc2c1,1,test
Cc1cn(-c2cc(N)cc(C(F)(F)F)c2)cn1,1,test
ClC(Cl)C(Cl)Cl,1,test
ClCc1ccc(-c2ccc(CCl)cc2)cc1,1,test
Clc1cccc2ccccc12,1,test
FC(F)(F)C=CC(F)(F)F,1,test
Fc1ccc2c(c1)CCC(C1CO1)O2,1,test
N#Cc1ccc(NC(=N)N)cc1,1,test
NCCCNC1CCCCC1,1,test
NP(N)(=O)Nc1ccccc1[N+](=O)[O-],1,test
Nc1ccc(Cc2ccc(N)c(Cl)c2)cc1Cl,1,test
Nc1ccc(C(F)(F)F)cc1,1,test
O=C(OCCCc1ccccc1)c1ccccc1,1,test
O=C1CCN(c2ccccc2)N1,1,test
O=Cc1ccc2c(c1)OCO2,1,test
O=C(O)CCC1(CCC(=O)O)CCCC(CCC(=O)O)(CCC(=O)O)C1=O,1,test
O=C(O)Cc1ccc([N+](=O)[O-])cc1,1,test
OCC1OC(OC2OC(CO)C(O)C(O)C2O)C(O)C(O)C1O,1,test
[N-]=[N+]=C1C=Cc2c(cccc2S(=O)(=O)O)C1=O,1,test
Oc1ccc(Cl)c(O)c1,1,test
S=C(Nc1ccccc1)Nc1ccccc1,1,test
[C-]#[C-],1,test
c1ccc(-c2ccccc2-c2ccccc2)cc1,1,test
N=C(N)N,1,test
CC12CCC3c4ccc(O)cc4CCC3C1CC(O)C2O,1,test
O=C(O)c1c(Cl)cc(Cl)cc1Cl,1,test
CSc1ncnc2nc[nH]c12,1,test
O=c1[nH]c(=O)n(C2CC(O)C(CO)O2)cc1F,1,test
O=C(O)C1CC(O)CN1,1,test
CN1C2CCC1CC(OC(=O)C(CO)c1ccccc1)C2,1,test
S=P(N1CC1)(N1CC1)N1CC1,1,test
CC12CCC(=O)C=C1CCC1C2C(=O)CC2(C)C1CCC2(O)C(=O)CO,1,test
CC(=O)N(O)c1ccc2c(c1)Cc1ccccc1-2,1,test
O=C1N=C(O)CN1N=Cc1ccc([N+](=O)[O-])o1,1,test
O=[N+]([O-])O[Hg]c1ccccc1,1,test
CCCC[Sn](CCCC)(CCCC)OC(C)=O,1,test
ON=Cc1cc[n+](CCC[n+]2ccc(C=NO)cc2)cc1,1,test
OCC1OC(OC2(CO)OC(CO)C(O)C2O)C(O)C(O)C1O,1,test
CC(=O)C1CCC2C3CCC4=CC(=O)CCC4(C)C3CCC12C,1,test
C=C(CC)C(=O)c1ccc(OCC(=O)O)c(Cl)c1Cl,1,test
CN(Cc1cnc2nc(N)nc(N)c2n1)c1ccc(C(=O)NC(CCC(=O)O)C(=O)O)cc1,1,test
O=c1[nH]c2ccccc2o1,1,test
CN(C)C(=O)Oc1cccc([N+](C)(C)C)c1,1,test
COc1ccc2c(c1)N(CCCN(C)C)c1ccccc1S2,1,test
CN(C)c1ccc2nc3ccc(=[N+](C)C)cc-3sc2c1,1,test
COP(=O)(OC)OC=C(Cl)Cl,1,test
CCOS(=O)(=O)OCC,1,test
c1cnc2c(c1)ccc1cccnc12,1,test
Cc1cc(C)[nH]n1,1,test
O=c1[nH]cnc2nc[nH]c12,1,test
CN(N=O)C(=N)N[N+](=O)[O-],1,test
CC(N)(CCC(=O)O)C(=O)O,1,test
Nc1nc2nc[nH]c2c(=O)[nH]1,1,test
Cc1c(C2(c3cc(C(C)C)c(O)c(Br)c3C)OS(=O)(=O)c3ccccc32)cc(C(C)C)c(O)c1Br,1,test
CN1CCC23CCCCC2C1Cc1ccc(O)cc13,1,test
CCC(C)(O)CC,1,test
CCCC[Sn]1(CCCC)OC(=O)C=CC(=O)O1,1,test
COP(=S)(OC)SCc1nc(N)nc(N)n1,1,test
O=S1(=O)N2CN3CN1CN(C2)S3(=O)=O,1,test
CCCCOS(=O)(=O)c1ccccc1,1,test
Nc1c(Br)cc(Br)c2c1C(=O)c1ccccc1C2=O,1,test
O=C1c2cc(O)c(O)c(O)c2C(=O)c2cc(O)c(O)c(O)c21,1,test
O=C(O)C(c1ccc(Cl)cc1)c1ccc(Cl)cc1,1,test
NC(=O)c1nc[nH]c1C(N)=O,1,test
O=C1C=C(O)c2ccccc2C1=O,1,test
CCCCCCCCCOC(=O)c1ccccc1C(=O)OCCCCCCCCC,1,test
O=S(=O)(O)c1cccc2ccccc12,1,test
Cc1c(Cc2ccccc2)c(=O)oc2cc(O)ccc12,1,test
CC1(C)SC2C(NC(=O)COc3ccccc3)C(=O)N2C1C(=O)O,1,test
CN(C)Cc1c[nH]c2ccccc12,1,test
Cc1cc(O)cc(C)c1Cl,1,test
O=C(O)c1cc(I)cc(I)c1I,1,test
Cc1ccc(C)c([N+](=O)[O-])c1,1,test
CCc1ccccc1O,1,test
OC1c2ccccc2Oc2ccccc21,1,test
O=C(c1ccc(Cl)cc1)c1ccc(Cl)cc1,1,test
CN(C)CCN(Cc1cccs1)c1ccccn1,1,test
c1ccc(N2CCOCC2)cc1,1,test
COc1ccc(CO)cc1OC,1,test
BrCC(Br)c1ccccc1,1,test
O=C(O)CCCOc1cc(Cl)c(Cl)cc1Cl,1,test
C=CCc1ccc2c(c1)OCO2,1,test
CCCC(O)C(CC)CO,1,test
Cc1ccccc1F,1,test
O=C(O)CNC(=S)C(=S)NCC(=O)O,1,test
CNC(=O)NC,1,test
CCC(Cc1c(I)cc(I)c(N)c1I)C(=O)O,1,test
O=Cc1cc([N+](=O)[O-])ccc1O,1,test
CC(C)(C)c1ccccc1,1,test
OB(O)c1ccccc1,1,test
COC12CC(CO)CN(C)C1Cc1cn(C)c3cccc2c13,1,test
O=[N+]([O-])C1(Br)COCOC1,1,test
CC(=O)Nc1ccc(Oc2cc(N3CCN(Cc4ccccc4-c4ccc(Cl)cc4)CC3)ccc2C(=O)NS(=O)(=O)c2ccc(NCC3CCOCC3)c([N+](=O)[O-])c2)cc1,1,test
CC(CCC[C@@H](C)[C@H]1CC[C@H]2[C@@H]3[C@H](O)C[C@H]4C[C@@H](NCCCNCCCCNCCCN)CC[C@]4(C)[C@H]3CC[C@]12C)C(=O)O,1,test
CCCCCCCCCCC(CO)CCCCCCCC,1,test
CCCCC(CC)C(=O)O.CCCCC(CC)C(=O)O.CCCCC(CC)C(=O)O.[Ce+3],1,test
C=CCCCCCCCCO,1,test
O=C(Cl)CCl,1,test
CCC(=O)OC1CC2CC1C1CC=CC21,1,test
C=CCOc1ccc(S(=O)(=O)c2ccc(O)cc2)cc1,1,test
C=CS(=O)(=O)CS(=O)(=O)C=C,1,test
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCN)NC1=O,1,test
CCCCCCCCCCCCOCCOCC(CC(=O)O)S(=O)(=O)O,1,test
CCO[Si](CNC1CCCCC1)(OCC)OCC,1,test
CN(C(=O)c1ccc(OCC2CC2)cc1)[C@@H]1Cc2ccc(CN3CCCCCC3)cc2C1,1,test
CC(C)/C(O)=C1/C(=O)O[C@@H](c2ccoc2)[C@]2(C)C(O)[C@@H](O)[C@@]34OC5(C)OC67CC(C)(C(O)[C@]6(O)[C@@H](O)[C@]3(O5)C12)[C@H](CC(=O)O)[C@]74C,1,test
CCCCC(CC)COC(=O)C(C)O,1,test
CCN(CC)CCNC(=O)c1ccc(NC(C)=O)cc1,1,test
Cc1ccc(NC(=O)c2ccc(CN3CCN(C)CC3)cc2)cc1N=C(N)N,1,test
Cc1cc(C)nc(C)c1,1,test
C=CCOCC(=C)C(=O)OC,1,test
CC(O)C(=O)O.CCCCC(CC)COC(=O)Nc1ccc(C)c(NC(=O)OCCN(C)C)c1,1,test
COC(=O)c1ccccc1OS(=O)(=O)c1ccc(O)cc1,1,test
CCCCCCCC/C=C\CCCCCCCC(=O)NCCC[N+](C)(C)CC.CCCCCCCC/C=C\CCCCCCCCCCCC(=O)NCCC[N+](C)(C)CC.CCOS(=O)(=O)OCC,1,test
COC(=O)C(=O)c1ccccc1,1,test
COc1cccc2c1ncc1c(=O)n(-c3cc(C)cs3)c(=O)n(C3CCCC3)c12,1,test
Cc1ccc(S(=O)(=O)c2ccc(O)c(O)c2)cc1,1,test
CC(C)(N)CO.CCCCCCCCCCCCc1ccccc1S(=O)(=O)O,1,test
CCCCCCCCCCCC(=O)N(C)CC(=O)O,1,test
C1=C(c2cccnc2)CCNC1,1,test
CC(=O)OC1(C)CCOC(CC(C)C)C1,1,test
CNS(=O)(=O)CC1CCC(N(C)c2ncnc3[nH]ccc23)CC1,1,test
CCCCC(CC)CO,1,test
C=CC(C)(CCC=C(C)C)OC(=O)CC,1,test
CCCCC(CC)C(=O)OOC(C)(C)CC,1,test
CC(C)c1cccc(C(C)C)c1,1,test
C=C[Si](C)(N(C)C)N(C)C,1,test
C=CCCCCCCO,1,test
CC(C)COC(=O)C(C)C,1,test
CC/C=C(\CCC)C(=O)O,1,test
C/C=C/C(=O)OC(C)CC(C)C,1,test
CC(=O)Oc1cccc(OC(C)=O)c1,1,test
CSCC[C@H](NC(=O)[C@H](CC(C)C)NC(=O)CNC(=O)[C@H](Cc1ccccc1)NC(=O)[C@@H](Cc1ccccc1)NC(=O)[C@H](CCC(N)=O)NC(=O)[C@@H](CCC(N)=O)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](CCCCN)NC(=O)[C@@H]1CCCN1C(=O)[C@@H](N)CCCN=C(N)N)C(N)=O,1,test
CCN(CC)c1ccc2c(-c3ccccc3C(=O)O)c3ccc(=[N+](CC)CC)cc-3oc2c1.O=[Mo]=O,1,test
C=CS(=O)(=O)c1ccc(N=Nc2c(S(=O)(=O)O)cc3cc(S(=O)(=O)O)c(N=Nc4ccc(S(=O)(=O)C=C)cc4S(=O)(=O)O)c(O)c3c2N)cc1,1,test
C[N+](C)(CCO)CCCNC(=O)C(O)C(O)C(O)C(O)CO,1,test
C=CC(=O)OCCCCOC(=O)C=C,1,test
CCCCO[P+](C)=O,1,test
CCCCC/C=C/C/C=C/CCCCCCCCC(=O)NCCC[N+](C)(C)CC(=O)O,1,test
CC(=O)N1c2ccccc2C(C)CC1(C)C,1,test
Cc1nc2cc(-c3ccccc3)c(-c3ccc(C4(N)CCC4)cc3)nn2c1/C=C/C(N)=O,1,test
COc1ccc(-c2ccc(C(C)C)cc2)c(C=O)c1O,1,test
CCCCCCCCCCCCP(=O)(O)O,1,test
CC(C)(C)C(=O)OC1CCC2(C)C3CCC4(C)C(=O)C5CC5C4C3C(O)C3OC32C1,1,test
CC1(C)CCCC2(C)C1CCC1(C)OC(=O)CC12,1,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1CC)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)CC,1,test
CN1CC[C@@H](c2c(O)cc(O)c3c(=O)cc(-c4ccccc4Cl)oc23)[C@@H]1CO,1,test
Nc1ccc(C(=O)N[C@@H](CCC(=O)O)C(=O)O)cc1,1,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]1CCCCN1C(C)C)C1CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N(C)OC,1,test
CCN(CC)c1ccc2nc3ccc(O)cc3[n+](-c3ccccc3)c2c1,1,test
CCCCCCCCC=CCCCCCCCC(=O)O[Sn](C)(C)OC(=O)CCCCCCCC=CCCCCCCCC,1,test
Cc1cc(CC(C)CC(C)(C)C)n(O)c(=O)c1.NCCO,1,test
CCCCOP(C)(=O)CCC(C#N)OC(C)=O,1,test
ClCc1ccc(Cl)nc1,1,test
CCC(C)C1CCCc2cccnc21,1,test
Cc1ccc(S(=O)(=O)n2cc(B3OC(C)(C)C(C)(C)O3)c3cc(F)cnc32)cc1,1,test
CCCCNCCCC,1,test
CC(C)(C)N,1,test
C=CCOC(=O)CCCCCC,1,test
N=C(N)NCCC[C@H](N)C(=O)O.N[C@@H](CCC(=O)O)C(=O)O,1,test
CC1CC(=O)C=C2CCC3C4CCC(=O)C4(C)CCC3C21C,1,test
CCCCCCCCCCCC(=O)NC(CCCN=C(N)N)C(=O)OCC,1,test
O=S(=O)(O)c1cc(Nc2nc(Nc3ccccc3)nc(N3CCOCC3)n2)ccc1C=Cc1ccc(Nc2nc(Nc3ccccc3)nc(N3CCOCC3)n2)cc1S(=O)(=O)O,1,test
C=CCOC(=O)CCC1CCCCC1,1,test
NNC(=NCCCC(=O)O)NN,1,test
CCc1nccn1CCC#N,1,test
CC(C)(C)CCCCCC(=O)O.CC(C)(C)CCCCCC(=O)O.[Sn+2],1,test
N#CC(CCc1ccc(Cl)cc1)(Cn1cncn1)c1ccccc1,1,test
C[Si](C)(C)N[Si](C)(C)C,1,test
Clc1ccccc1C(Cl)(Cl)Cl,1,test
CC(C)(N)CO.CCCCCCCCC=CCCCCCCCC(=O)O,1,test
CC(C)CCCCCCCCCCO,1,test
CC1(C)CC=C(CCC=O)CC1,1,test
CC(C)COC(=S)S,1,test
O=C(O)C1=NN(c2ccc(S(=O)(=O)O)cc2)C(=O)C1N=Nc1ccc(S(=O)(=O)O)cc1.O=C(O)C1=NN(c2ccc(S(=O)(=O)O)cc2)C(=O)C1N=Nc1ccc(S(=O)(=O)O)cc1.O=C(O)C1=NN(c2ccc(S(=O)(=O)O)cc2)C(=O)C1N=Nc1ccc(S(=O)(=O)O)cc1.[Al+3],1,test
C=CS(=O)(=O)c1ccc(Nc2nc(Cl)nc(Nc3ccc4c(O)c(N=Nc5cc([N+](=O)[O-])ccc5O)c(S(=O)(=O)O)cc4c3)n2)cc1.O=[N+]([O-])c1ccc2c(N=Nc3c(O)ccc4ccccc34)ccc(S(=O)(=O)O)c2c1.[Cr+3],1,test
CN=C(c1ccccc1Cl)C1(O)CCCC1,1,test
CN1c2ccccc2C(=O)c2ccc(Cl)cc2S1(=O)=O,1,test
C=C(C)CCOC(=O)C(=C)C,1,test
ClCC=Cc1ccccc1,1,test
Nc1cc(Cl)c(OC(F)(F)C(F)C(F)(F)F)cc1Cl,1,test
CC(=CCCC=O)CCCC(C)C,1,test
Clc1cccc(C(Cl)(Cl)Cl)n1,1,test
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CC(=O)NCCCC[C@@H](C(N)=O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](Cc2cccc(C(N)=O)c2)NC1=O,1,test
BrC(Br)Br,1,test
C=C[C@@H]1C[C@]1(NC(=O)[C@@H]1C[C@@]2(CN1C(=O)[C@@H](NC(=O)CC1(CC#N)CCCCC1)C(C)(C)C)C(C)(C)C21CCC1)C(=O)NS(=O)(=O)N1CCCC1,1,test
N#CCC1(n2cc(C(N)=O)c(Nc3ccnc(F)c3)n2)CCC(Nc2ccccn2)CC1,1,test
CN(C)Cc1cc(C(C)(C)c2cc(CN(C)C)c(O)c(CN(C)C)c2)cc(CN(C)C)c1O.CN(C)Cc1cc(C(C)(C)c2cc(CN(C)C)c(O)c(CN(C)C)c2)ccc1O,1,test
c1cc(OCC2CO2)cc(N(CC2CO2)CC2CO2)c1,1,test
CCCCCCCC(=O)O.CCCCCCCC(=O)O.[Zn+2],1,test
O=C(O)CNCO,1,test
CCCCCCCC/C=C/C=O,1,test
CCCCCCCCCCCCCCCCCC(=O)NCCC[N+](C)(C)CC(=O)NCCO,1,test
CCCCOC(=O)CC(CC(=O)OCCCC)(OC(C)=O)C(=O)OCCCC,1,test
CCCCC(CC)CO[P+](=O)OCC(CC)CCCC,1,test
CCOC(=O)[C@@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@@H]1CC2C=C[C@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@@H]1C2.CCOC(=O)[C@H]1CC2C=C[C@H]1C2,1,test
O=C1C2CC=CCC2C(=O)N1SC(Cl)(Cl)Cl,1,test
CCCC[n+]1ccccc1.[Al+3].[Al+3],1,test
CC(C)CCCCCOC(=O)CCS,1,test
CC(C)=CCCC(C)=CCC1CCCC1=O,1,test
CC(=O)N[C@@H](CCCNC(=N)N)C(=O)N[C@H]1CCC(=O)NCCC[C@@H](C(=O)O)NC(=O)[C@H](Cc2c[nH]c3ccccc23)NC(=O)[C@H](CCCNC(=N)N)NC(=O)[C@@H](Cc2ccccc2)NC(=O)[C@H](CCCCN)NC1=O,1,test
CCCCCCCCCCCCNCCCNCC(=O)O.CCCCCCCCCCCCNCCNCCN,1,test
O=Nc1ccc(NO)cc1,1,test
CCOC(=O)C(CC1(CC)CCCN2CCc3c([nH]c4ccccc34)C21)=NO,1,test
C=CC(O)C(=O)OC,1,test
C[N+](C)(C)CCCCC[C@H](N)C(=O)O,1,test
NC(=O)N=C(N)N.NC(=O)N=C(N)N.[Cu+2],1,test
CC(=O)[CH-]C(C)=O.CC(=O)[CH-]C(C)=O.[Zn+2],1,test
CCCCCCCCCCCC[N+](C)(C)CC(=O)NCCN(CCO)CCC(=O)O.O=C(O)CCNCCN(CCO)CCC(=O)O,1,test
COc1ccc(C)cc1C(CCN(C(C)C)C(C)C)c1ccccc1,1,test
Cc1ccc(N)c(N)c1.Cc1cccc(N)c1N,1,test
COc1ccc(C(=O)c2ccccc2O)c(O)c1,1,test
COc1ccc([N+](=O)[O-])cc1OC(C)=O,1,test
CCC(C)=CCCC(C)CCO,1,test
CC(CC(=O)OOC(C)(C)C)CC(C)(C)C,1,test
CC(O)CN(CCCN(CCCN(C)C)CCCN(C)C)CC(C)O,1,test
CC1(C)CC(N)CC(C)(CN)C1.Cc1ccccc1OCC(O)CNC1CC(C)(C)CC(C)(CN)C1.Cc1ccccc1OCC(O)CNCC1(C)CC(N(CC(O)COc2ccccc2C)CC(O)COc2ccccc2C)CC(C)(C)C1.Cc1ccccc1OCC(O)CNCC1(C)CC(NCC(O)COc2ccccc2C)CC(C)(C)C1,1,test
CC(C)(C)CC(C)(C)CC(=O)O.CC(C)(C)CC(C)(C)CC(=O)O.[Mn+2],1,test
CC(CO)O[Ti](OCCOCCO)OCCN(CCO)CCO,1,test
CCCC=C(C#N)c1ccccc1,1,test
C=C(C)C(=O)NCCC[N+](C)(C)CC(=O)NCCC[N+](C)(C)CC(O)C[N+](C)(C)C,1,test
CC(=O)[CH-]C(C)=O.CC(=O)[CH-]C(C)=O.[Co+2],1,test
CCCC[C@H]1CO[C@H](c2ccc(Br)c(F)c2)OC1,1,test
CCC(C)Nc1ccc(Cc2ccc(NC(C)CC)cc2)cc1,1,test
CN(C)C(=S)S.CN(C)C(=S)S.CN(C)C(=S)S.[Fe+3],1,test
C=CC(=O)OCCCCCCCCCCOC(=O)C=C,1,test
CC[Al](CC)CC,1,test
Cn1sc(Cl)cc1=O.Cn1sccc1=O,1,test
C=C(C)C(=O)OCCCCCCCCCCCCOC(=O)C(=C)C,1,test
CCCCCCCCCCCCCC[N+](C)(C)C.CCCCCCCCCCCC[N+](C)(C)C.COS(=O)(=O)OC,1,test
