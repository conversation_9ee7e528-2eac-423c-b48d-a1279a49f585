import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from sklearn.metrics.pairwise import cosine_similarity
from utils.MY_GNN import MGA
import warnings
warnings.filterwarnings('ignore')

class RealModelSimilarityAnalyzer:
    """从真实模型文件中分析TFP-G参数相似性"""
    
    def __init__(self, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        
    def load_model_checkpoint(self, model_path):
        """加载模型checkpoint并返回state_dict"""
        if not os.path.exists(model_path):
            print(f"模型文件不存在: {model_path}")
            return None
            
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                return checkpoint['model_state_dict']
            else:
                return checkpoint
        except Exception as e:
            print(f"加载模型失败 {model_path}: {str(e)}")
            return None
    
    def extract_tfp_parameters_from_state_dict(self, state_dict, model_name):
        """从state_dict中提取TFP-G相关参数"""
        tfp_params = {}
        
        # 提取注意力权重参数
        attention_params = {}
        for key, param in state_dict.items():
            if 'weighted_sum_readout.atom_weighting_specific' in key:
                attention_params[key] = param.cpu().numpy().flatten()
            elif 'weighted_sum_readout.shared_weighting' in key:
                attention_params[key] = param.cpu().numpy().flatten()
        
        # 提取全连接层参数
        fc_params = {}
        for key, param in state_dict.items():
            if any(fc_layer in key for fc_layer in ['fc_layers1', 'fc_layers2', 'fc_layers3', 'output_layer1']):
                fc_params[key] = param.cpu().numpy().flatten()
        
        # 合并所有TFP-G参数
        tfp_params.update(attention_params)
        tfp_params.update(fc_params)
        
        print(f"从 {model_name} 提取了 {len(tfp_params)} 个TFP-G参数")
        return tfp_params, attention_params, fc_params
    
    def compute_parameter_similarity(self, params1, params2, method='cosine'):
        """计算两组参数的相似性"""
        # 找到共同的参数键
        common_keys = set(params1.keys()) & set(params2.keys())
        
        if not common_keys:
            print("没有找到共同的参数键")
            return 0.0
        
        # 将参数连接成向量
        vec1 = np.concatenate([params1[key] for key in sorted(common_keys)])
        vec2 = np.concatenate([params2[key] for key in sorted(common_keys)])
        
        if method == 'cosine':
            similarity = cosine_similarity([vec1], [vec2])[0, 0]
        elif method == 'correlation':
            correlation_matrix = np.corrcoef(vec1, vec2)
            similarity = correlation_matrix[0, 1] if not np.isnan(correlation_matrix[0, 1]) else 0.0
        elif method == 'euclidean':
            distance = np.linalg.norm(vec1 - vec2)
            similarity = 1 / (1 + distance)
        else:
            raise ValueError(f"未知的相似性方法: {method}")
        
        return similarity
    
    def analyze_real_model_similarity(self, model_paths_dict, output_dir='real_similarity_results'):
        """分析真实模型的TFP-G参数相似性"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 加载所有模型的参数
        all_tfp_params = {}
        all_attention_params = {}
        all_fc_params = {}
        
        valid_models = []
        
        for model_name, model_path in model_paths_dict.items():
            state_dict = self.load_model_checkpoint(model_path)
            if state_dict is not None:
                tfp_params, attention_params, fc_params = self.extract_tfp_parameters_from_state_dict(
                    state_dict, model_name
                )
                if tfp_params:  # 确保提取到了参数
                    all_tfp_params[model_name] = tfp_params
                    all_attention_params[model_name] = attention_params
                    all_fc_params[model_name] = fc_params
                    valid_models.append(model_name)
        
        if len(valid_models) < 2:
            print("需要至少2个有效模型进行相似性分析")
            return None
        
        print(f"成功加载 {len(valid_models)} 个模型: {valid_models}")
        
        # 计算整体TFP-G参数相似性矩阵
        n_models = len(valid_models)
        similarity_matrix = np.zeros((n_models, n_models))
        
        for i, model1 in enumerate(valid_models):
            for j, model2 in enumerate(valid_models):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    similarity = self.compute_parameter_similarity(
                        all_tfp_params[model1], all_tfp_params[model2], method='cosine'
                    )
                    similarity_matrix[i, j] = similarity
        
        # 绘制整体相似性热力图
        self.plot_similarity_heatmap(
            similarity_matrix, valid_models, 
            'TFP-G Parameter Similarity (All Parameters)',
            f'{output_dir}/tfp_overall_similarity.png'
        )
        
        # 计算注意力权重相似性矩阵
        attention_similarity_matrix = np.zeros((n_models, n_models))
        
        for i, model1 in enumerate(valid_models):
            for j, model2 in enumerate(valid_models):
                if i == j:
                    attention_similarity_matrix[i, j] = 1.0
                else:
                    similarity = self.compute_parameter_similarity(
                        all_attention_params[model1], all_attention_params[model2], method='cosine'
                    )
                    attention_similarity_matrix[i, j] = similarity
        
        # 绘制注意力权重相似性热力图
        self.plot_similarity_heatmap(
            attention_similarity_matrix, valid_models,
            'Attention Weights Similarity',
            f'{output_dir}/attention_similarity.png'
        )
        
        # 计算FC层参数相似性矩阵
        fc_similarity_matrix = np.zeros((n_models, n_models))
        
        for i, model1 in enumerate(valid_models):
            for j, model2 in enumerate(valid_models):
                if i == j:
                    fc_similarity_matrix[i, j] = 1.0
                else:
                    similarity = self.compute_parameter_similarity(
                        all_fc_params[model1], all_fc_params[model2], method='cosine'
                    )
                    fc_similarity_matrix[i, j] = similarity
        
        # 绘制FC层相似性热力图
        self.plot_similarity_heatmap(
            fc_similarity_matrix, valid_models,
            'FC Layers Parameter Similarity',
            f'{output_dir}/fc_similarity.png'
        )
        
        # 保存相似性矩阵到CSV
        similarity_df = pd.DataFrame(similarity_matrix, index=valid_models, columns=valid_models)
        similarity_df.to_csv(f'{output_dir}/tfp_similarity_matrix.csv')
        
        attention_similarity_df = pd.DataFrame(attention_similarity_matrix, index=valid_models, columns=valid_models)
        attention_similarity_df.to_csv(f'{output_dir}/attention_similarity_matrix.csv')
        
        fc_similarity_df = pd.DataFrame(fc_similarity_matrix, index=valid_models, columns=valid_models)
        fc_similarity_df.to_csv(f'{output_dir}/fc_similarity_matrix.csv')
        
        # 打印相似性结果
        print("\n整体TFP-G参数相似性:")
        for i, model1 in enumerate(valid_models):
            for j, model2 in enumerate(valid_models):
                if i < j:
                    print(f"{model1} vs {model2}: {similarity_matrix[i, j]:.4f}")
        
        return {
            'overall': (similarity_matrix, valid_models),
            'attention': (attention_similarity_matrix, valid_models),
            'fc': (fc_similarity_matrix, valid_models)
        }
    
    def plot_similarity_heatmap(self, similarity_matrix, labels, title, output_path, figsize=(10, 8)):
        """绘制相似性热力图"""
        plt.figure(figsize=figsize)
        
        # 创建热力图
        sns.heatmap(similarity_matrix, 
                   xticklabels=labels, 
                   yticklabels=labels,
                   annot=True, 
                   cmap='RdYlGn', 
                   vmin=0, vmax=1,
                   square=True,
                   fmt='.3f',
                   cbar_kws={'label': 'Similarity'})
        
        plt.title(title, fontsize=14, fontweight='bold')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"热力图已保存: {output_path}")

def main():
    """主函数"""
    print("真实模型TFP-G参数相似性分析")
    print("=" * 50)
    
    # 创建分析器
    analyzer = RealModelSimilarityAnalyzer()
    
    # 定义模型路径
    model_paths = {
        'MTL-scr': 'model/MTL-scr_early_stop.pth',
        'STL-scr': 'model/STL-scr_early_stop.pth', 
        'FishLC50': 'model/FishLC50_early_stop.pth',
        'FishEL_NOEC': 'model/FishEL_NOEC_early_stop.pth',
        'MTL': 'model/MTL_early_stop.pth'
    }
    
    # 执行相似性分析
    results = analyzer.analyze_real_model_similarity(model_paths)
    
    if results:
        print("\n分析完成！结果已保存到 real_similarity_results/ 目录")
    else:
        print("分析失败，请检查模型文件路径")

if __name__ == "__main__":
    main()
