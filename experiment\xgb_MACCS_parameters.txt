FishLC50:{'max_depth': 9, 'min_child_weight': 2, 'gamma': 0.04, 'reg_lambda': 1, 'reg_alpha': 1, 'lr': 0.05, 'n_estimators': 200, 'colsample_bytree': 0.75, 'subsample': 0.85}
FishEL_NOEC:{'max_depth': 6, 'min_child_weight': 1, 'gamma': 0.14, 'reg_lambda': 1, 'reg_alpha': 0.01, 'lr': 0.005, 'n_estimators': 160, 'colsample_bytree': 0.85, 'subsample': 0.75}
DMRepNOEC:{'max_depth': 8, 'min_child_weight': 4, 'gamma': 0.1, 'reg_lambda': 0.1, 'reg_alpha': 0.1, 'lr': 0.05, 'n_estimators': 260, 'colsample_bytree': 0.85, 'subsample': 0.8}
DMImbEC50:{'max_depth': 9, 'min_child_weight': 5, 'gamma': 0.0, 'reg_lambda': 1e-05, 'reg_alpha': 0.01, 'lr': 0.01, 'n_estimators': 160, 'colsample_bytree': 0.75, 'subsample': 0.8}
AlaGroErC50:{'max_depth': 4, 'min_child_weight': 2, 'gamma': 0.0, 'reg_lambda': 1, 'reg_alpha': 1, 'lr': 0.05, 'n_estimators': 240, 'colsample_bytree': 0.8, 'subsample': 0.75}
