#!/usr/bin/env python3
"""
测试截断方法的FC层相似性计算
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

def test_truncation_method():
    """测试截断方法"""
    print("=" * 60)
    print("测试截断方法的FC层相似性计算")
    print("=" * 60)
    
    # 模拟不同维度的FC参数
    print("模拟场景:")
    
    # 设置随机种子以获得可重复的结果
    np.random.seed(42)
    
    # FishAT: 128维FC层参数 (鱼类任务1)
    # 使用特定的分布模式
    fish_at_base = np.random.normal(0.1, 0.05, 64)  # 前64维：鱼类共同特征
    fish_at_specific = np.random.normal(0.15, 0.03, 64)  # 后64维：FishAT特有特征
    fish_at_params = np.concatenate([fish_at_base, fish_at_specific])
    
    # FishCT: 64维FC层参数 (鱼类任务2)
    # 前64维应该与FishAT的前64维相似（鱼类共同特征）
    fish_ct_params = fish_at_base + np.random.normal(0, 0.01, 64)  # 添加少量噪声
    
    # DMCT: 64维FC层参数 (大型溞类任务1)
    # 使用不同的分布特征
    dm_ct_params = np.random.normal(-0.05, 0.08, 64)
    
    # DMAT: 64维FC层参数 (大型溞类任务2)
    # 与DMCT相似
    dm_at_params = dm_ct_params + np.random.normal(0, 0.02, 64)
    
    print(f"FishAT参数: {len(fish_at_params)}维, 均值={np.mean(fish_at_params):.4f}")
    print(f"FishCT参数: {len(fish_ct_params)}维, 均值={np.mean(fish_ct_params):.4f}")
    print(f"DMCT参数: {len(dm_ct_params)}维, 均值={np.mean(dm_ct_params):.4f}")
    print(f"DMAT参数: {len(dm_at_params)}维, 均值={np.mean(dm_at_params):.4f}")
    
    # 应用截断方法
    print(f"\n应用截断方法:")
    dimensions = [len(fish_at_params), len(fish_ct_params), len(dm_ct_params), len(dm_at_params)]
    min_dim = min(dimensions)
    print(f"最小维度: {min_dim}")
    
    # 截断到最小维度
    fish_at_truncated = fish_at_params[:min_dim]
    fish_ct_truncated = fish_ct_params[:min_dim]
    dm_ct_truncated = dm_ct_params[:min_dim]
    dm_at_truncated = dm_at_params[:min_dim]
    
    print(f"FishAT: 从{len(fish_at_params)}维截断到{len(fish_at_truncated)}维")
    print(f"FishCT: 保持{len(fish_ct_truncated)}维不变")
    print(f"DMCT: 保持{len(dm_ct_truncated)}维不变")
    print(f"DMAT: 保持{len(dm_at_truncated)}维不变")
    
    # 计算相似性矩阵
    params_matrix = np.array([fish_at_truncated, fish_ct_truncated, dm_ct_truncated, dm_at_truncated])
    similarity_matrix = cosine_similarity(params_matrix)
    
    labels = ['FishAT', 'FishCT', 'DMCT', 'DMAT']
    
    print(f"\n截断方法相似性矩阵:")
    print("        ", "  ".join(f"{label:>8}" for label in labels))
    for i, label in enumerate(labels):
        row = "  ".join(f"{similarity_matrix[i][j]:8.4f}" for j in range(len(labels)))
        print(f"{label:>8}: {row}")
    
    # 分析结果
    fish_at_ct_sim = similarity_matrix[0][1]  # FishAT vs FishCT
    fish_ct_dm_ct_sim = similarity_matrix[1][2]  # FishCT vs DMCT
    dm_ct_at_sim = similarity_matrix[2][3]  # DMCT vs DMAT
    
    print(f"\n关键相似性对比:")
    print(f"FishAT vs FishCT: {fish_at_ct_sim:.4f} (鱼类任务间)")
    print(f"FishCT vs DMCT: {fish_ct_dm_ct_sim:.4f} (鱼类与DM任务间)")
    print(f"DMCT vs DMAT: {dm_ct_at_sim:.4f} (DM任务间)")
    
    # 验证改进效果
    print(f"\n结果分析:")
    if fish_at_ct_sim > fish_ct_dm_ct_sim:
        print(f"✅ 截断方法成功：鱼类任务间相似性({fish_at_ct_sim:.4f}) > 鱼类与DM任务相似性({fish_ct_dm_ct_sim:.4f})")
    else:
        print(f"❌ 仍需改进：鱼类任务间相似性({fish_at_ct_sim:.4f}) < 鱼类与DM任务相似性({fish_ct_dm_ct_sim:.4f})")
    
    if dm_ct_at_sim > fish_ct_dm_ct_sim:
        print(f"✅ DM任务间相似性({dm_ct_at_sim:.4f}) > 跨物种相似性({fish_ct_dm_ct_sim:.4f})")
    else:
        print(f"⚠️  DM任务间相似性({dm_ct_at_sim:.4f}) < 跨物种相似性({fish_ct_dm_ct_sim:.4f})")
    
    return similarity_matrix

def compare_methods():
    """对比不同方法的效果"""
    print(f"\n" + "=" * 60)
    print("方法对比总结:")
    print("=" * 60)
    
    print("1. 零填充方法（原始）:")
    print("   - 问题：人为增加相似性")
    print("   - 结果：维度相同的任务相似性过高")
    
    print("\n2. 统计特征方法:")
    print("   - 问题：丢失原始参数信息")
    print("   - 结果：可能过度抽象")
    
    print("\n3. 截断方法（当前）:")
    print("   - 优势：保持原始参数特性")
    print("   - 原理：使用共同维度进行比较")
    print("   - 合理性：比较参数的重叠部分")

def main():
    """主函数"""
    print("测试截断方法的FC层相似性计算...")
    test_truncation_method()
    compare_methods()
    
    print(f"\n" + "=" * 60)
    print("截断方法已实施到代码中")
    print("建议重新运行任务B验证实际效果")
    print("=" * 60)

if __name__ == "__main__":
    main()
