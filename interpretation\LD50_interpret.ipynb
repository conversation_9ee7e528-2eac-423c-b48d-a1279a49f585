{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"is_executing": false, "name": "#%%\n"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Conda\\envs\\GenAI\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***************************************************************************************************\n", "toxicity, 1/1 time\n", "***************************************************************************************************\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '../data/toxicity_group.csv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 102\u001b[0m\n\u001b[0;32m    100\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m/\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m time\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mformat(args[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtask_name\u001b[39m\u001b[38;5;124m'\u001b[39m], time_id \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m, args[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtimes\u001b[39m\u001b[38;5;124m'\u001b[39m]))\n\u001b[0;32m    101\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m***************************************************************************************************\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m--> 102\u001b[0m train_set, val_set, test_set, task_number \u001b[38;5;241m=\u001b[39m \u001b[43mbuild_dataset\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_graph_from_csv_bin_for_splited\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    103\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbin_path\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mbin_path\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    104\u001b[0m \u001b[43m    \u001b[49m\u001b[43mgroup_path\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mgroup_path\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    105\u001b[0m \u001b[43m    \u001b[49m\u001b[43mselect_task_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mselect_task_index\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[0;32m    106\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    107\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMolecule graph generation is complete !\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    108\u001b[0m train_loader \u001b[38;5;241m=\u001b[39m DataLoader(dataset\u001b[38;5;241m=\u001b[39mtrain_set,\n\u001b[0;32m    109\u001b[0m                           batch_size\u001b[38;5;241m=\u001b[39margs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbatch_size\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[0;32m    110\u001b[0m                           shuffle\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[0;32m    111\u001b[0m                           collate_fn\u001b[38;5;241m=\u001b[39mcollate_molgraphs)\n", "File \u001b[1;32me:\\manuscript\\水生毒性的集成预测\\筛查\\code\\interpretation\\..\\utils\\build_dataset.py:375\u001b[0m, in \u001b[0;36mload_graph_from_csv_bin_for_splited\u001b[1;34m(bin_path, group_path, select_task_index)\u001b[0m\n\u001b[0;32m    371\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mload_graph_from_csv_bin_for_splited\u001b[39m(\n\u001b[0;32m    372\u001b[0m         bin_path\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m../data/AquaTox_scr.bin\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m    373\u001b[0m         group_path\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m../data/AquaTox_scr_group.csv\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m    374\u001b[0m         select_task_index\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON>one\u001b[39;00m):\n\u001b[1;32m--> 375\u001b[0m     smiles \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mgroup_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex_col\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39msmiles\u001b[38;5;241m.\u001b[39mvalues\n\u001b[0;32m    376\u001b[0m     group \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(group_path, index_col\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m)\u001b[38;5;241m.\u001b[39mgroup\u001b[38;5;241m.\u001b[39mto_list()\n\u001b[0;32m    377\u001b[0m     graphs, detailed_information \u001b[38;5;241m=\u001b[39m load_graphs(bin_path)\n", "File \u001b[1;32md:\\Conda\\envs\\GenAI\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:948\u001b[0m, in \u001b[0;36mread_csv\u001b[1;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[0;32m    935\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[0;32m    936\u001b[0m     dialect,\n\u001b[0;32m    937\u001b[0m     delimiter,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    944\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[0;32m    945\u001b[0m )\n\u001b[0;32m    946\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[1;32m--> 948\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Conda\\envs\\GenAI\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:611\u001b[0m, in \u001b[0;36m_read\u001b[1;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[0;32m    608\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[0;32m    610\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[1;32m--> 611\u001b[0m parser \u001b[38;5;241m=\u001b[39m TextFileReader(filepath_or_buffer, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[0;32m    613\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[0;32m    614\u001b[0m     \u001b[38;5;28;01mret<PERSON>\u001b[39;00m parser\n", "File \u001b[1;32md:\\Conda\\envs\\GenAI\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1448\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[1;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[0;32m   1445\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m   1447\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m-> 1448\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Conda\\envs\\GenAI\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1705\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[1;34m(self, f, engine)\u001b[0m\n\u001b[0;32m   1703\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[0;32m   1704\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m-> 1705\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1706\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1707\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1708\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1709\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcompression\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1710\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmemory_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1711\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1712\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding_errors\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstrict\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1713\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstorage_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1714\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1715\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1716\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[1;32md:\\Conda\\envs\\GenAI\\lib\\site-packages\\pandas\\io\\common.py:863\u001b[0m, in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    858\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m    859\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[0;32m    860\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[0;32m    861\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[0;32m    862\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[1;32m--> 863\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[0;32m    864\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    865\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    866\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    867\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    868\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    869\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    870\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    871\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m    872\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '../data/toxicity_group.csv'"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["import numpy as np\n", "import sys\n", "sys.path.append('../')\n", "from utils import build_dataset\n", "import torch\n", "from torch.optim import Adam\n", "from torch.utils.data import DataLoader\n", "from utils.MY_GNN import collate_molgraphs, EarlyStopping, run_an_eval_epoch_heterogeneous_return_weight,\\\n", " set_random_seed, MGA\n", "from utils.MY_GNN import pos_weight\n", "import os\n", "import time\n", "import pandas as pd\n", "\n", "start = time.time()\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"7\"\n", "\n", "# fix parameters of model\n", "args = {}\n", "args['device'] = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "args['atom_data_field'] = 'atom'\n", "args['bond_data_field'] = 'etype'\n", "args['classification_metric_name'] = 'roc_auc'\n", "args['regression_metric_name'] = 'r2'\n", "# model parameter\n", "args['num_epochs'] = 500\n", "args['patience'] = 50\n", "args['batch_size'] = 128\n", "args['mode'] = 'higher'\n", "args['in_feats'] = 40\n", "args['rgcn_hidden_feats'] = [64, 64]\n", "args['classifier_hidden_feats'] = 64\n", "args['rgcn_drop_out'] = 0.2\n", "args['drop_out'] = 0.2\n", "args['lr'] = 3\n", "args['weight_decay'] = 5\n", "args['loop'] = True\n", "\n", "# task name (model name)\n", "args['task_name'] = 'interpretation'  # change\n", "args['data_name'] = 'AquaTox_scr'  # change\n", "args['times'] = 1\n", "\n", "# selected task, generate select task index, task class, and classification_num\n", "args['select_task_list'] = ['Carcinogenicity', 'Ames Mutagenicity', 'Respiratory toxicity',\n", "                             'Eye irritation', 'Eye corrosion', 'Cardiotoxicity1', 'Cardiotoxicity5',\n", "                             'Cardiotoxicity10', 'Cardiotoxicity30',\n", "                             'CYP1A2', 'CYP2C19', 'CYP2C9', 'CYP2D6', 'CYP3A4',\n", "                             'NR-AR', 'NR-AR-LBD', 'NR-AhR', 'NR-Aromatase', 'NR-ER', 'NR-ER-LBD',\n", "                             'NR-PPAR-gamma', 'SR-ARE', 'SR-ATAD5', 'SR-HSE', 'SR-MMP', 'SR-p53',\n", "                             'Acute oral toxicity (LD50)', 'LC50DM', 'BCF', 'LC50', 'IGC50'] # change\n", "args['select_task_index'] = []\n", "args['classification_num'] = 0\n", "args['regression_num'] = 0\n", "args['all_task_list'] =['Acute oral toxicity (LD50)', 'LC50DM', 'BCF', 'LC50', 'IGC50', 'Urinary_tract_toxicity']# change\n", "# generate select task index\n", "for index, task in enumerate(args['all_task_list']):\n", "    if task in args['select_task_list']:\n", "        args['select_task_index'].append(index)\n", "# generate classification_num\n", "for task in args['select_task_list']:\n", "    if task in ['Carcinogenicity', 'Ames Mutagenicity', 'Respiratory toxicity',\n", "                'Eye irritation', 'Eye corrosion', 'Cardiotoxicity1', 'Cardiotoxicity5',\n", "                'Cardiotoxicity10', 'Cardiotoxicity30',\n", "                'CYP1A2', 'CYP2C19', 'CYP2C9', 'CYP2D6', 'CYP3A4',\n", "                'NR-AR', 'NR-AR-LBD', 'NR-AhR', 'NR-Aromatase', 'NR-ER', 'NR-ER-LBD',\n", "                'NR-PPAR-gamma', 'SR-ARE', 'SR-ATAD5', 'SR-HSE', 'SR-MMP', 'SR-p53', 'Hepatotoxicity']:\n", "        args['classification_num'] = args['classification_num'] + 1\n", "    if task in ['Acute oral toxicity (LD50)', 'LC50DM', 'BCF', 'LC50', 'IGC50', 'Urinary_tract_toxicity']:\n", "        args['regression_num'] = args['regression_num'] + 1\n", "# generate classification_num\n", "if args['classification_num'] != 0 and args['regression_num'] != 0:\n", "    args['task_class'] = 'classification_regression'\n", "if args['classification_num'] != 0 and args['regression_num'] == 0:\n", "    args['task_class'] = 'classification'\n", "if args['classification_num'] == 0 and args['regression_num'] != 0:\n", "    args['task_class'] = 'regression'\n", "\n", "args['bin_path'] = '../data/' + args['data_name'] + '.bin'\n", "args['group_path'] = '../data/' + args['data_name'] + '_group.csv'\n", "\n", "result_pd = pd.DataFrame(columns=args['select_task_list'] + ['group'] + args['select_task_list'] + ['group']\n", "                                 + args['select_task_list'] + ['group'])\n", "all_times_train_result = []\n", "all_times_val_result = []\n", "all_times_test_result = []\n", "for time_id in range(args['times']):\n", "    set_random_seed(2020 + time_id)\n", "    one_time_train_result = []\n", "    one_time_val_result = []\n", "    one_time_test_result = []\n", "    print('***************************************************************************************************')\n", "    print('{}, {}/{} time'.format(args['task_name'], time_id + 1, args['times']))\n", "    print('***************************************************************************************************')\n", "    train_set, val_set, test_set, task_number = build_dataset.load_graph_from_csv_bin_for_splited(\n", "        bin_path=args['bin_path'],\n", "        group_path=args['group_path'],\n", "        select_task_index=args['select_task_index']\n", "    )\n", "    print(\"Molecule graph generation is complete !\")\n", "    train_loader = DataLoader(dataset=train_set,\n", "                              batch_size=args['batch_size'],\n", "                              shuffle=True,\n", "                              collate_fn=collate_molgraphs)\n", "\n", "    val_loader = DataLoader(dataset=val_set,\n", "                            batch_size=args['batch_size'],\n", "                            shuffle=True,\n", "                            collate_fn=collate_molgraphs)\n", "\n", "    test_loader = DataLoader(dataset=test_set,\n", "                             batch_size=args['batch_size'],\n", "                             collate_fn=collate_molgraphs)\n", "    pos_weight_np = pos_weight(train_set, classification_num=args['classification_num'])\n", "    loss_criterion_c = torch.nn.BCEWithLogitsLoss(reduction='none', pos_weight=pos_weight_np.to(args['device']))\n", "    loss_criterion_r = torch.nn.MSELoss(reduction='none')\n", "    model = MGA(in_feats=args['in_feats'], rgcn_hidden_feats=args['rgcn_hidden_feats'],\n", "                   n_tasks=31, rgcn_drop_out=args['rgcn_drop_out'],\n", "                   classifier_hidden_feats=args['classifier_hidden_feats'], dropout=args['drop_out'],\n", "                   loop=args['loop'], return_weight=True)\n", "    optimizer = Adam(model.parameters(), lr=10 ** -args['lr'], weight_decay=10 ** -args['weight_decay'])\n", "    stopper = EarlyStopping(patience=args['patience'],\n", "                            task_name=args['task_name'], mode=args['mode'])\n", "    model.to(args['device'])\n", "    stopper.load_checkpoint(model)\n", "    # selected test molecules\n", "    selected_data = pd.read_csv('data for interpretation/Acute oral toxicity (LD50)_test_for_interpretation.csv')\n", "    selected_mol_list = selected_data['smiles'].tolist()\n", "    # visual test set\n", "    run_an_eval_epoch_heterogeneous_return_weight(args, model, test_loader, vis_list=selected_mol_list)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "GenAI", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "pycharm": {"stem_cell": {"cell_type": "raw", "metadata": {"collapsed": false}, "source": []}}}, "nbformat": 4, "nbformat_minor": 1}