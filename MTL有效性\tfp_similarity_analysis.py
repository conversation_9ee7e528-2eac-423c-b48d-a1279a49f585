import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics.pairwise import cosine_similarity
from scipy.spatial.distance import pdist, squareform
import os
from utils.MY_GNN import MGA
import warnings
warnings.filterwarnings('ignore')

class TFPSimilarityAnalyzer:
    def __init__(self, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        self.models = {}
        self.tfp_parameters = {}
        
    def load_model_from_checkpoint(self, model_path, model_config):
        """从checkpoint加载模型"""
        model = MGA(**model_config)
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
            
        model.to(self.device)
        model.eval()
        return model
    
    def extract_tfp_parameters(self, model, model_name):
        """提取TFP-G相关参数"""
        tfp_params = {}
        
        # 提取注意力权重参数 (WeightAndSum中的atom_weighting_specific)
        for i, attention_layer in enumerate(model.weighted_sum_readout.atom_weighting_specific):
            for name, param in attention_layer.named_parameters():
                key = f"attention_task_{i}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()
        
        # 提取共享注意力权重参数
        for name, param in model.weighted_sum_readout.shared_weighting.named_parameters():
            key = f"shared_attention_{name}"
            tfp_params[key] = param.detach().cpu().numpy().flatten()
        
        # 提取全连接层参数
        for i in range(model.task_num):
            # FC层1
            for name, param in model.fc_layers1[i].named_parameters():
                key = f"fc1_task_{i}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()
            
            # FC层2
            for name, param in model.fc_layers2[i].named_parameters():
                key = f"fc2_task_{i}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()
            
            # FC层3
            for name, param in model.fc_layers3[i].named_parameters():
                key = f"fc3_task_{i}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()
            
            # 输出层
            for name, param in model.output_layer1[i].named_parameters():
                key = f"output_task_{i}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()
        
        self.tfp_parameters[model_name] = tfp_params
        return tfp_params
    
    def create_modified_mga_models(self, base_config, fc_layer_configs):
        """创建不同FC层数的MGA模型用于分析A"""
        modified_models = {}
        
        for config_name, fc_config in fc_layer_configs.items():
            # 创建修改后的BaseGNN类
            class ModifiedMGA(MGA):
                def __init__(self, fc_layers_config, **kwargs):
                    super().__init__(**kwargs)
                    
                    # 重新定义FC层结构
                    self.fc_layers_config = fc_layers_config
                    self.fc_layers1 = torch.nn.ModuleList()
                    self.fc_layers2 = torch.nn.ModuleList()
                    self.fc_layers3 = torch.nn.ModuleList()
                    
                    for i in range(self.task_num):
                        if fc_layers_config['num_layers'] >= 1:
                            self.fc_layers1.append(self.fc_layer(
                                kwargs.get('dropout', 0.2), 
                                self.fc_in_feats, 
                                fc_layers_config['hidden_dims'][0]
                            ))
                        
                        if fc_layers_config['num_layers'] >= 2:
                            self.fc_layers2.append(self.fc_layer(
                                kwargs.get('dropout', 0.2),
                                fc_layers_config['hidden_dims'][0],
                                fc_layers_config['hidden_dims'][1] if len(fc_layers_config['hidden_dims']) > 1 
                                else fc_layers_config['hidden_dims'][0]
                            ))
                        
                        if fc_layers_config['num_layers'] >= 3:
                            self.fc_layers3.append(self.fc_layer(
                                kwargs.get('dropout', 0.2),
                                fc_layers_config['hidden_dims'][1] if len(fc_layers_config['hidden_dims']) > 1 
                                else fc_layers_config['hidden_dims'][0],
                                fc_layers_config['hidden_dims'][2] if len(fc_layers_config['hidden_dims']) > 2 
                                else fc_layers_config['hidden_dims'][-1]
                            ))
                
                def forward(self, bg, node_feats, etype, norm=None):
                    # GNN特征提取
                    for gnn in self.gnn_layers:
                        node_feats = gnn(bg, node_feats, etype, norm)
                    
                    # TFP-G处理
                    if self.return_weight:
                        feats_list, atom_weight_list = self.weighted_sum_readout(bg, node_feats)
                    else:
                        feats_list = self.weighted_sum_readout(bg, node_feats)
                    
                    # 根据配置的层数进行前向传播
                    for i in range(self.task_num):
                        mol_feats = feats_list[i]
                        
                        if self.fc_layers_config['num_layers'] >= 1:
                            h1 = self.fc_layers1[i](mol_feats)
                            current_feats = h1
                        
                        if self.fc_layers_config['num_layers'] >= 2:
                            h2 = self.fc_layers2[i](current_feats)
                            current_feats = h2
                        
                        if self.fc_layers_config['num_layers'] >= 3:
                            h3 = self.fc_layers3[i](current_feats)
                            current_feats = h3
                        
                        predict = self.output_layer1[i](current_feats)
                        
                        if i == 0:
                            prediction_all = predict
                        else:
                            prediction_all = torch.cat([prediction_all, predict], dim=1)
                    
                    if self.return_mol_embedding:
                        return feats_list[0]
                    else:
                        if self.return_weight:
                            return prediction_all, atom_weight_list, node_feats
                        return prediction_all
            
            # 创建模型实例
            model = ModifiedMGA(fc_config, **base_config)
            model.to(self.device)
            modified_models[config_name] = model
        
        return modified_models
    
    def compute_parameter_similarity(self, params1, params2, method='cosine'):
        """计算两组参数之间的相似性"""
        # 将参数字典转换为向量
        common_keys = set(params1.keys()) & set(params2.keys())
        
        if not common_keys:
            return 0.0
        
        vec1 = np.concatenate([params1[key] for key in sorted(common_keys)])
        vec2 = np.concatenate([params2[key] for key in sorted(common_keys)])
        
        if method == 'cosine':
            similarity = cosine_similarity([vec1], [vec2])[0, 0]
        elif method == 'correlation':
            similarity = np.corrcoef(vec1, vec2)[0, 1]
        elif method == 'euclidean':
            similarity = 1 / (1 + np.linalg.norm(vec1 - vec2))
        else:
            raise ValueError(f"Unknown similarity method: {method}")
        
        return similarity
    
    def analyze_fc_layer_similarity(self, base_model_path, base_config, fc_configs, output_dir='analysis_results'):
        """分析任务A：不同FC层配置的相似性"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 加载基础模型（3层FC）
        base_model = self.load_model_from_checkpoint(base_model_path, base_config)
        base_params = self.extract_tfp_parameters(base_model, 'base_3fc')
        
        # 创建不同FC层配置的模型
        modified_models = self.create_modified_mga_models(base_config, fc_configs)
        
        # 计算相似性矩阵
        model_names = ['base_3fc'] + list(fc_configs.keys())
        similarity_matrix = np.zeros((len(model_names), len(model_names)))
        
        # 提取所有模型的参数
        all_params = {'base_3fc': base_params}
        for name, model in modified_models.items():
            # 随机初始化参数用于比较
            with torch.no_grad():
                for param in model.parameters():
                    param.normal_(0, 0.1)
            all_params[name] = self.extract_tfp_parameters(model, name)
        
        # 计算相似性
        for i, name1 in enumerate(model_names):
            for j, name2 in enumerate(model_names):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    similarity = self.compute_parameter_similarity(
                        all_params[name1], all_params[name2], method='cosine'
                    )
                    similarity_matrix[i, j] = similarity
        
        # 绘制热力图
        plt.figure(figsize=(10, 8))
        sns.heatmap(similarity_matrix, 
                   xticklabels=model_names, 
                   yticklabels=model_names,
                   annot=True, 
                   cmap='RdYlGn', 
                   vmin=0, vmax=1,
                   square=True)
        plt.title('TFP-G Parameter Similarity: Different FC Layer Configurations')
        plt.tight_layout()
        plt.savefig(f'{output_dir}/fc_layer_similarity_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return similarity_matrix, model_names

    def analyze_task_similarity(self, model_paths_dict, model_configs_dict, output_dir='analysis_results'):
        """分析任务B：不同毒性任务之间的TFP-G参数相似性"""
        os.makedirs(output_dir, exist_ok=True)

        # 加载所有任务的模型
        task_models = {}
        task_params = {}

        for task_name, model_path in model_paths_dict.items():
            if os.path.exists(model_path):
                config = model_configs_dict.get(task_name, model_configs_dict['default'])
                model = self.load_model_from_checkpoint(model_path, config)
                task_models[task_name] = model
                task_params[task_name] = self.extract_tfp_parameters(model, task_name)
                print(f"Loaded model for task: {task_name}")
            else:
                print(f"Model file not found: {model_path}")

        if len(task_params) < 2:
            print("Need at least 2 models for similarity analysis")
            return None, None

        # 计算任务间相似性矩阵
        task_names = list(task_params.keys())
        similarity_matrix = np.zeros((len(task_names), len(task_names)))

        for i, task1 in enumerate(task_names):
            for j, task2 in enumerate(task_names):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    similarity = self.compute_parameter_similarity(
                        task_params[task1], task_params[task2], method='cosine'
                    )
                    similarity_matrix[i, j] = similarity

        # 绘制热力图
        plt.figure(figsize=(12, 10))
        sns.heatmap(similarity_matrix,
                   xticklabels=task_names,
                   yticklabels=task_names,
                   annot=True,
                   cmap='RdYlGn',
                   vmin=0, vmax=1,
                   square=True,
                   fmt='.3f')
        plt.title('TFP-G Parameter Similarity: Different Toxicity Tasks')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/task_similarity_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 保存相似性矩阵到CSV
        similarity_df = pd.DataFrame(similarity_matrix,
                                   index=task_names,
                                   columns=task_names)
        similarity_df.to_csv(f'{output_dir}/task_similarity_matrix.csv')

        return similarity_matrix, task_names

    def analyze_attention_weights_similarity(self, model_paths_dict, model_configs_dict, output_dir='analysis_results'):
        """专门分析注意力权重的相似性"""
        os.makedirs(output_dir, exist_ok=True)

        # 加载所有任务的模型
        attention_params = {}

        for task_name, model_path in model_paths_dict.items():
            if os.path.exists(model_path):
                config = model_configs_dict.get(task_name, model_configs_dict['default'])
                model = self.load_model_from_checkpoint(model_path, config)

                # 只提取注意力权重参数
                attention_weights = {}
                for i, attention_layer in enumerate(model.weighted_sum_readout.atom_weighting_specific):
                    for name, param in attention_layer.named_parameters():
                        key = f"attention_task_{i}_{name}"
                        attention_weights[key] = param.detach().cpu().numpy().flatten()

                attention_params[task_name] = attention_weights
                print(f"Extracted attention weights for task: {task_name}")

        if len(attention_params) < 2:
            print("Need at least 2 models for attention similarity analysis")
            return None, None

        # 计算注意力权重相似性矩阵
        task_names = list(attention_params.keys())
        attention_similarity_matrix = np.zeros((len(task_names), len(task_names)))

        for i, task1 in enumerate(task_names):
            for j, task2 in enumerate(task_names):
                if i == j:
                    attention_similarity_matrix[i, j] = 1.0
                else:
                    similarity = self.compute_parameter_similarity(
                        attention_params[task1], attention_params[task2], method='cosine'
                    )
                    attention_similarity_matrix[i, j] = similarity

        # 绘制注意力权重相似性热力图
        plt.figure(figsize=(12, 10))
        sns.heatmap(attention_similarity_matrix,
                   xticklabels=task_names,
                   yticklabels=task_names,
                   annot=True,
                   cmap='RdYlGn',
                   vmin=0, vmax=1,
                   square=True,
                   fmt='.3f')
        plt.title('Attention Weights Similarity: Different Toxicity Tasks')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/attention_similarity_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()

        return attention_similarity_matrix, task_names
