"""
任务A直接运行脚本

直接运行FC层配置相似性分析，无需交互
"""

import os
import sys
import matplotlib.pyplot as plt

# 添加路径以导入模块
sys.path.append('.')
from task_a.fc_layer_analyzer import FCLayerAnalyzer


def main():
    """主函数 - 直接运行任务A"""
    # 设置字体为Times New Roman (与任务B字体设置方式一致)
    plt.rcParams['font.family'] = 'serif'
    plt.rcParams['font.serif'] = 'Times New Roman'
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['axes.unicode_minus'] = False
    
    print("=" * 60)
    print("任务A：FC层配置相似性分析 - 直接运行模式")
    print("=" * 60)
    
    # 创建分析器
    analyzer = FCLayerAnalyzer()
    
    # 基础模型路径
    base_model_path = '../model/MTL-scr_early_stop.pth'
    
    # 检查模型文件
    if not os.path.exists(base_model_path):
        print(f"❌ 错误：基础模型文件不存在: {base_model_path}")
        return
    
    try:
        # 获取默认配置
        base_config = analyzer.get_default_base_config()
        
        # 直接执行分析
        similarity_matrix, model_names = analyzer.analyze_fc_layer_similarity(
            base_model_path, base_config
        )
        
        print("\n✅ 分析完成！结果已保存到 task_a_results/ 目录")
        
    except Exception as e:
        print(f"❌ 分析出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
