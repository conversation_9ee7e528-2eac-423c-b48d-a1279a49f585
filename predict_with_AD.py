#!/usr/bin/env python
# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import os
import sys

import torch
from rdkit import Chem
from rdkit.Chem import MolFromSmiles
import dgl
import warnings
warnings.filterwarnings('ignore')
# 导入模块
from utils.MY_GNN import MGA

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f" 使用设备: {device}")

# 尝试导入应用域判断模块（可选）
try:
    from adsal import NSG
    HAS_ADSAL = True
except ImportError:
    print(" 未找到adsal模块，应用域判断功能将不可用")
    HAS_ADSAL = False
    NSG = None

# =============================================================================
# 配置参数 - 请在这里修改你的设置
# =============================================================================

# 预测模式选择：'file', 'single', 'test'
PREDICTION_MODE = 'test'  # 改为test模式进行测试

# 输入输出文件配置
INPUT_FILE = '../IECSC/IECSC_2025.xlsx'        # 输入的化合物文件
OUTPUT_FILE = 'prediction/IECSC_2025_prediction_with_AD_results.xlsx'  # 输出文件名

# 单个SMILES预测参数（当PREDICTION_MODE='single'时使用）
SINGLE_SMILES = 'CCO'  # 要预测的SMILES

# 最佳应用域参数
OPTIMAL_DENSLB = 1e-5    # 最佳相似性密度阈值
OPTIMAL_LDUB = 0.8     # 最佳局域不连续性阈值

# 模型和数据路径
MODEL_PATH = 'model/MTL-scr_early_stop.pth'  # MTL-scr模型路径
TRAINING_DATA_PATH = 'data/AquaTox_scr.csv'  # 训练数据路径

# 批处理参数
BATCH_SIZE = 128
# MTL-scr模型参数（与最新训练脚本保持一致）
MODEL_ARGS = {
    'in_feats': 40,
    'rgcn_hidden_feats': [128, 128],  # 与Toxicity_MGA_MT_Adaptive.py一致
    'n_tasks': 5,  # 5个任务：FishLC50, FishEL_NOEC, DMRepNOEC, DMImbEC50, AlaGroErC50
    'classifier_hidden_feats': 128,   # 与Toxicity_MGA_MT_Adaptive.py一致
    'rgcn_drop_out': 0.2,            # 与Toxicity_MGA_MT_Adaptive.py一致
    'dropout': 0.2,                  # 与Toxicity_MGA_MT_Adaptive.py一致
    'loop': True
}

# 任务名称列表（与训练时保持一致）
TASK_NAMES = ['FishLC50', 'FishEL_NOEC', 'DMRepNOEC', 'DMImbEC50', 'AlaGroErC50']  # FishLC50, 'FishEL_NOEC', 'DMRepNOEC', 'DMImbEC50', 'AlaGroErC50'

# 图构建参数
GRAPH_ARGS = {
    'atom_data_field': 'atom',
    'bond_data_field': 'etype'
}

# =============================================================================
# 图构建和预测功能函数
# =============================================================================

def construct_molecule_graph(smiles):
    """构建分子图 - 与训练时保持完全一致"""
    from utils.build_dataset import construct_RGCN_bigraph_from_smiles

    try:
        # 使用与训练时相同的图构建函数
        g = construct_RGCN_bigraph_from_smiles(smiles)
        return g
    except Exception as e:
        raise ValueError(f"构建分子图失败 - SMILES: {smiles}, 错误: {str(e)}")

class MTLScrPredictor:
    """基于MGA模型的多任务水生毒性预测器"""

    def __init__(self, model_path, model_args=None):
        """初始化预测器"""
        self.model_path = model_path
        self.device = device
        self.model_args = model_args or MODEL_ARGS
        self.task_names = TASK_NAMES

        self._load_model()

    def _load_model(self):
        """加载MGA模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        print(f" 加载MTL-scr模型: {self.model_path}")
        print(f" 使用设备: {self.device}")

        # 构建模型 - 使用与训练时一致的参数名
        self.model = MGA(
            in_feats=self.model_args['in_feats'],
            rgcn_hidden_feats=self.model_args['rgcn_hidden_feats'],
            n_tasks=self.model_args['n_tasks'],
            classifier_hidden_feats=self.model_args['classifier_hidden_feats'],
            rgcn_drop_out=self.model_args['rgcn_drop_out'],
            drop_out=self.model_args['dropout'],  # 注意：训练脚本中使用的是drop_out而不是dropout
            loop=self.model_args['loop']
        )

        # 加载模型权重 - 兼容不同的保存格式
        checkpoint = torch.load(self.model_path, map_location=self.device)

        # 检查checkpoint格式
        if 'model_state_dict' in checkpoint:
            # 新格式：包含完整训练状态
            self.model.load_state_dict(checkpoint['model_state_dict'])
            print(f" 加载完整checkpoint (epoch: {checkpoint.get('epoch', 'unknown')})")
        else:
            # 旧格式：直接是模型状态字典
            self.model.load_state_dict(checkpoint)
            print(f" 加载模型状态字典")

        # 移动模型到设备并设置为评估模式
        self.model.to(self.device)
        self.model.eval()

        print(f" MTL-scr模型加载成功 (设备: {self.device})")

    def _validate_smiles(self, smiles):
        """验证SMILES字符串"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return False, "无效的SMILES字符串"

            canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
            return True, canonical_smiles
        except Exception as e:
            return False, f"SMILES处理错误: {str(e)}"

    def predict_single(self, smiles):
        """预测单个SMILES的多任务毒性"""
        is_valid, result = self._validate_smiles(smiles)
        if not is_valid:
            return {'smiles': smiles, 'error': result}

        canonical_smiles = result

        try:
            # 构建分子图
            g = construct_molecule_graph(canonical_smiles)

            # 批处理图
            bg = dgl.batch([g]).to(self.device)
            atom_feats = bg.ndata[GRAPH_ARGS['atom_data_field']].to(self.device)
            bond_feats = bg.edata[GRAPH_ARGS['bond_data_field']].to(self.device)

            with torch.no_grad():
                # 获取多任务预测结果
                predictions = self.model(bg, atom_feats, bond_feats)
                predictions = torch.sigmoid(predictions).cpu().numpy()[0]  # 应用sigmoid并转换为numpy

            # 构建结果字典
            result_dict = {
                'smiles': smiles,
                'canonical_smiles': canonical_smiles,
                'error': None
            }

            # 为每个任务添加预测结果
            for i, task_name in enumerate(self.task_names):
                probability = float(predictions[i])
                prediction = int(probability > 0.5)
                result_dict[f'{task_name}_prediction'] = prediction
                result_dict[f'{task_name}_probability'] = probability
                result_dict[f'{task_name}_label'] = '有毒' if prediction == 1 else '无毒'

            return result_dict

        except Exception as e:
            return {'smiles': smiles, 'error': f"预测错误: {str(e)}"}

def predict_on_input_file(input_file_path, model_path=None, output_file_path=None):
    """
    对输入文件进行MTL-scr多任务预测

    Args:
        input_file_path: 输入文件路径（Excel格式）
        model_path: 模型文件路径，如果为None则使用默认模型
        output_file_path: original_file_predicted
    """
    print("开始对输入文件进行MTL-scr多任务预测...")

    # 1. 检查输入文件
    if not os.path.exists(input_file_path):
        print(f"输入文件不存在: {input_file_path}")
        return

    # 2. 确定模型路径
    if model_path is None:
        model_path = MODEL_PATH

    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        return

    # 3. 确定输出文件路径
    if output_file_path is None:
        base_name = os.path.splitext(input_file_path)[0]
        output_file_path = f"{base_name}_predicted.xlsx"

    print(f"输入文件: {input_file_path}")
    print(f"模型文件: {model_path}")
    print(f"输出文件: {output_file_path}")
    print(f"使用设备: {device}")

    # 4. 加载MTL-scr模型
    try:
        # 构建模型 - 使用与训练时一致的参数名
        model = MGA(
            in_feats=MODEL_ARGS['in_feats'],
            rgcn_hidden_feats=MODEL_ARGS['rgcn_hidden_feats'],
            n_tasks=MODEL_ARGS['n_tasks'],
            classifier_hidden_feats=MODEL_ARGS['classifier_hidden_feats'],
            rgcn_drop_out=MODEL_ARGS['rgcn_drop_out'],
            drop_out=MODEL_ARGS['dropout'],  # 注意：训练脚本中使用的是drop_out而不是dropout
            loop=MODEL_ARGS['loop']
        )

        # 加载模型权重 - 兼容不同的保存格式
        checkpoint = torch.load(model_path, map_location=device)

        # 检查checkpoint格式
        if 'model_state_dict' in checkpoint:
            # 新格式：包含完整训练状态
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"加载完整checkpoint (epoch: {checkpoint.get('epoch', 'unknown')})")
        else:
            # 旧格式：直接是模型状态字典
            model.load_state_dict(checkpoint)
            print(f"加载模型状态字典")

        # 移动模型到设备并设置为评估模式
        model.to(device)
        model.eval()

        print(f"MTL-scr模型加载成功（设备: {device}）")
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        return

    # 5. 加载输入数据
    try:
        df = pd.read_excel(input_file_path)
        print(f"样本数量: {len(df)}")

        # 检查必要列
        required_columns = ['smiles']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"输入数据缺少必要列: {missing_columns}")
            return

    except Exception as e:
        print(f"数据加载失败: {str(e)}")
        return

    # 6. 准备结果DataFrame
    result_df = df.copy()
    result_df['canonical_smiles'] = ''
    result_df['error'] = ''

    # 为每个任务添加预测列
    for task_name in TASK_NAMES:
        result_df[f'{task_name}_prediction'] = -1
        result_df[f'{task_name}_probability'] = 0.0
        result_df[f'{task_name}_label'] = ''

    # 7. 验证SMILES并进行预测
    print("开始多任务预测...")
    valid_indices = []
    valid_canonical_smiles = []

    for idx, row in df.iterrows():
        smiles = row['smiles']
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                result_df.loc[idx, 'error'] = "无效的SMILES字符串"
                continue

            canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
            result_df.loc[idx, 'canonical_smiles'] = canonical_smiles
            valid_indices.append(idx)
            valid_canonical_smiles.append(canonical_smiles)

        except Exception as e:
            result_df.loc[idx, 'error'] = f"SMILES处理错误: {str(e)}"

    print(f"有效样本: {len(valid_indices)}/{len(df)}")

    if len(valid_indices) == 0:
        print("没有有效的样本可以预测")
        return

    # 8. 批量预测
    try:
        batch_size = BATCH_SIZE
        batch_list = [valid_indices[i:i+batch_size] for i in range(0, len(valid_indices), batch_size)]

        for batch_idx, batch_indices in enumerate(batch_list):
            print(f"处理批次 {batch_idx + 1}/{len(batch_list)}")

            batch_smiles = [valid_canonical_smiles[valid_indices.index(idx)] for idx in batch_indices]

            # 构建分子图
            graphs = []
            for smi in batch_smiles:
                try:
                    graphs.append(construct_molecule_graph(smi))
                except Exception as e:
                    print(f"构建图失败: {smi}, {str(e)}")
                    graphs.append(None)

            # 过滤有效图
            valid_graphs = [g for g in graphs if g is not None]
            if not valid_graphs:
                print(f"批次 {batch_idx + 1} 没有有效的分子图")
                continue

            # 批处理图
            bg = dgl.batch(valid_graphs).to(device)
            atom_feats = bg.ndata[GRAPH_ARGS['atom_data_field']].to(device)
            bond_feats = bg.edata[GRAPH_ARGS['bond_data_field']].to(device)

            # 预测
            with torch.no_grad():
                predictions = model(bg, atom_feats, bond_feats)
                predictions = torch.sigmoid(predictions).cpu().numpy()  # 应用sigmoid

                # 保存预测结果
                valid_idx = 0
                for i, orig_idx in enumerate(batch_indices):
                    if graphs[i] is not None:  # 只处理有效图的结果
                        for j, task_name in enumerate(TASK_NAMES):
                            probability = float(predictions[valid_idx, j])
                            prediction = int(probability > 0.5)

                            result_df.loc[orig_idx, f'{task_name}_prediction'] = prediction
                            result_df.loc[orig_idx, f'{task_name}_probability'] = probability
                            result_df.loc[orig_idx, f'{task_name}_label'] = '有毒' if prediction == 1 else '无毒'
                        valid_idx += 1
                    else:
                        # 为无效图设置错误信息
                        result_df.loc[orig_idx, 'error'] = "分子图构建失败"

                # 清理GPU内存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

        print("多任务预测完成")

    except Exception as e:
        print(f"预测过程出错: {str(e)}")
        # 为所有有效索引标记错误
        for idx in valid_indices:
            result_df.loc[idx, 'error'] = f"预测错误: {str(e)}"

    # 9. 保存结果
    try:
        result_df.to_excel(output_file_path, index=False)
        print(f"结果已保存到: {output_file_path}")

        # 显示统计结果
        valid_predictions = result_df[result_df['error'] == '']
        if len(valid_predictions) > 0:
            print(f"\n多任务预测统计:")
            print(f"有效预测: {len(valid_predictions)}/{len(result_df)}")

            # 为每个任务显示统计
            for task_name in TASK_NAMES:
                pred_col = f'{task_name}_prediction'
                prob_col = f'{task_name}_probability'

                if pred_col in valid_predictions.columns:
                    high_toxicity_count = (valid_predictions[pred_col] == 1).sum()
                    low_toxicity_count = (valid_predictions[pred_col] == 0).sum()
                    avg_prob = valid_predictions[prob_col].mean()

                    print(f"\n{task_name}:")
                    print(f"  有毒: {high_toxicity_count} ({high_toxicity_count/len(valid_predictions)*100:.1f}%)")
                    print(f"  无毒: {low_toxicity_count} ({low_toxicity_count/len(valid_predictions)*100:.1f}%)")
                    print(f"  平均毒性概率: {avg_prob:.4f}")

        errors = result_df[result_df['error'] != '']
        if len(errors) > 0:
            print(f"\n无法预测样本: {len(errors)}")

    except Exception as e:
        print(f"保存结果失败: {str(e)}")

# exp权重函数
def expWt(x, a=15, eps=1e-6):
    """指数权重函数"""
    return np.exp(-a*(1-x)/(x + eps))

EXP_WEIGHT_PARAMS = {'a': 15}

# =============================================================================
# 核心功能函数
# =============================================================================

def load_training_data_for_task(file_path, task_name):
    """为特定任务加载训练集数据"""
    print(f"为任务 {task_name} 加载训练集数据: {file_path}")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"训练数据文件不存在: {file_path}")

    df = pd.read_csv(file_path)

    # 检查必要列
    required_cols = ['smiles']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"训练集缺少必要列: {missing_cols}")

    # 为特定任务创建标签
    if task_name in df.columns:
        df['y'] = df[task_name].fillna(0).astype(int)
        # 只保留该任务有有效数据的样本
        valid_samples = df[df[task_name].notna()]
        print(f"任务 {task_name} 有效训练样本数: {len(valid_samples)}")
    else:
        raise ValueError(f"训练集中没有找到任务列: {task_name}")

    # 准备数据
    df_clean = valid_samples[['smiles', 'y']].copy()
    df_clean.reset_index(drop=True, inplace=True)

    return df_clean

def load_training_data(file_path):
    """加载训练集数据 - 兼容旧版本调用"""
    print(f"加载训练集数据: {file_path}")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"训练数据文件不存在: {file_path}")

    df = pd.read_csv(file_path)

    # 检查必要列
    required_cols = ['smiles']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"训练集缺少必要列: {missing_cols}")

    # 创建一个综合标签用于整体应用域判断（保持向后兼容）
    task_cols = [col for col in TASK_NAMES if col in df.columns]
    if task_cols:
        df['y'] = 0
        for col in task_cols:
            df['y'] = df['y'] | df[col].fillna(0).astype(int)
    else:
        df['y'] = 0

    df_clean = df[['smiles', 'y']].copy()
    df_clean.reset_index(drop=True, inplace=True)

    print(f"训练集样本数: {len(df_clean)}")
    return df_clean

def run_prediction(input_file, model_path, temp_output):
    """运行毒性预测"""
    print("步骤1: 运行毒性预测")

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")

    # 运行预测
    predict_on_input_file(input_file, model_path, temp_output)

    if not os.path.exists(temp_output):
        raise RuntimeError("预测失败，未生成预测文件")

    print("预测完成")
    return temp_output

def calculate_ad_metrics_for_task(df_train, df_query, task_name):
    """为特定任务计算应用域指标"""
    print(f"步骤2: 为任务 {task_name} 计算应用域指标")

    # 创建NSG对象
    nsg = NSG(df_train, yCol='y', smiCol='smiles')

    # 计算分子指纹相似性
    nsg.calcPairwiseSimilarityWithFp('MACCS_keys')

    # 生成查询-训练相似性矩阵
    dfQTSM = nsg.genQTSM(df_query, 'smiles')

    # 计算应用域指标（使用exp权重函数）
    ad_metrics = nsg.queryADMetrics(
        dfQTSM,
        wtFunc1=expWt,
        kw1=EXP_WEIGHT_PARAMS,
        wtFunc2=expWt,
        kw2=EXP_WEIGHT_PARAMS,
        code='|exp'
    )

    # 为指标添加任务前缀
    ad_metrics_renamed = {}
    for col in ad_metrics.columns:
        ad_metrics_renamed[f'{task_name}_{col}'] = ad_metrics[col]

    ad_metrics_df = pd.DataFrame(ad_metrics_renamed, index=ad_metrics.index)

    print(f"任务 {task_name} 应用域指标计算完成")
    return ad_metrics_df

def calculate_ad_metrics(df_train, df_query):
    """计算应用域指标 - 兼容旧版本调用"""
    print("步骤2: 计算应用域指标")

    # 创建NSG对象
    nsg = NSG(df_train, yCol='y', smiCol='smiles')

    # 计算分子指纹相似性
    nsg.calcPairwiseSimilarityWithFp('MACCS_keys')

    # 生成查询-训练相似性矩阵
    dfQTSM = nsg.genQTSM(df_query, 'smiles')

    # 计算应用域指标（使用exp权重函数）
    ad_metrics = nsg.queryADMetrics(
        dfQTSM,
        wtFunc1=expWt,
        kw1=EXP_WEIGHT_PARAMS,
        wtFunc2=expWt,
        kw2=EXP_WEIGHT_PARAMS,
        code='|exp'
    )

    # 合并结果
    df_result = df_query.join(ad_metrics)

    print("应用域指标计算完成")
    return df_result

def apply_multi_task_ad_criteria(df_query, optimal_densLB, optimal_LdUB):
    """为多任务应用应用域判断标准"""
    print("步骤3: 多任务应用域判断")
    print(f"相似性密度阈值 (densLB): {optimal_densLB}")
    print(f"局域不连续性阈值 (LdUB): {optimal_LdUB}")

    if not HAS_ADSAL:
        print("⚠️ 应用域判断功能不可用，跳过应用域分析")
        # 为每个任务添加默认的应用域信息
        for task_name in TASK_NAMES:
            df_query[f'{task_name}_in_applicability_domain'] = True
            df_query[f'{task_name}_ad_reason'] = "应用域判断功能不可用"
        return df_query

    df_result = df_query.copy()

    # 为每个任务计算应用域
    for task_name in TASK_NAMES:
        print(f"\n处理任务: {task_name}")

        try:
            # 加载该任务的训练数据
            df_train_task = load_training_data_for_task(TRAINING_DATA_PATH, task_name)

            # 计算该任务的应用域指标
            ad_metrics_task = calculate_ad_metrics_for_task(df_train_task, df_query, task_name)

            # 合并应用域指标
            for col in ad_metrics_task.columns:
                df_result[col] = ad_metrics_task[col]

            # 应用域判断条件
            density_col = f'{task_name}_simiDensity|exp'
            ld_col = f'{task_name}_simiWtLD_w|exp'

            if density_col in df_result.columns and ld_col in df_result.columns:
                ad_condition = (
                    (df_result[density_col] >= optimal_densLB) &
                    (df_result[ld_col] <= optimal_LdUB)
                )

                df_result[f'{task_name}_in_applicability_domain'] = ad_condition
                df_result[f'{task_name}_ad_density_value'] = df_result[density_col]
                df_result[f'{task_name}_ad_ld_value'] = df_result[ld_col]

                # 添加应用域判断原因
                def get_task_ad_reason(row, task):
                    density_val = row[f'{task}_ad_density_value']
                    ld_val = row[f'{task}_ad_ld_value']

                    if row[f'{task}_in_applicability_domain']:
                        return f"{task}: 在应用域内"
                    else:
                        reasons = []
                        if density_val < optimal_densLB:
                            reasons.append(f"相似性密度({density_val:.3f}) < 阈值({optimal_densLB})")
                        if ld_val > optimal_LdUB:
                            reasons.append(f"局域不连续性({ld_val:.3f}) > 阈值({optimal_LdUB})")
                        return f"{task}: " + "; ".join(reasons)

                df_result[f'{task_name}_ad_reason'] = df_result.apply(
                    lambda row: get_task_ad_reason(row, task_name), axis=1
                )

                # 统计该任务的应用域结果
                total_compounds = len(df_result)
                in_domain_count = df_result[f'{task_name}_in_applicability_domain'].sum()
                print(f"  {task_name} 应用域内: {in_domain_count}/{total_compounds} ({in_domain_count/total_compounds*100:.1f}%)")

        except Exception as e:
            print(f"  ⚠️ 任务 {task_name} 应用域计算失败: {str(e)}")
            # 设置默认值
            df_result[f'{task_name}_in_applicability_domain'] = False
            df_result[f'{task_name}_ad_reason'] = f"应用域计算失败: {str(e)}"

    return df_result

def apply_ad_criteria(df_with_metrics, optimal_densLB, optimal_LdUB):
    """应用应用域判断标准 - 兼容旧版本调用"""
    print("步骤3: 应用域判断")

    print(f"相似性密度阈值 (densLB): {optimal_densLB}")
    print(f"局域不连续性阈值 (LdUB): {optimal_LdUB}")

    # 应用域判断条件
    ad_condition = (
        (df_with_metrics['simiDensity|exp'] >= optimal_densLB) &
        (df_with_metrics['simiWtLD_w|exp'] <= optimal_LdUB)
    )

    # 添加应用域判断结果
    df_result = df_with_metrics.copy()
    df_result['in_applicability_domain'] = ad_condition
    df_result['ad_densLB_threshold'] = optimal_densLB
    df_result['ad_LdUB_threshold'] = optimal_LdUB
    df_result['ad_density_value'] = df_with_metrics['simiDensity|exp']
    df_result['ad_ld_value'] = df_with_metrics['simiWtLD_w|exp']

    # 添加应用域判断原因
    def get_ad_reason(row):
        if row['in_applicability_domain']:
            return "在应用域内"
        else:
            reasons = []
            if row['ad_density_value'] < optimal_densLB:
                reasons.append(f"相似性密度({row['ad_density_value']:.3f}) < 阈值({optimal_densLB})")
            if row['ad_ld_value'] > optimal_LdUB:
                reasons.append(f"局域不连续性({row['ad_ld_value']:.3f}) > 阈值({optimal_LdUB})")
            return "; ".join(reasons)

    df_result['ad_reason'] = df_result.apply(get_ad_reason, axis=1)

    # 统计结果
    total_compounds = len(df_result)
    in_domain_count = df_result['in_applicability_domain'].sum()

    print(f"应用域判断结果:")
    print(f"总化合物数: {total_compounds}")
    print(f"应用域内: {in_domain_count} ({in_domain_count/total_compounds*100:.1f}%)")
    print(f"应用域外: {total_compounds - in_domain_count} ({(total_compounds - in_domain_count)/total_compounds*100:.1f}%)")

    return df_result

def generate_summary(df_result):
    """生成多任务结果摘要"""
    print("\n多任务预测结果摘要:")
    print("=" * 100)

    for idx, row in df_result.iterrows():
        compound_name = row.get('compound_name', f'化合物_{idx+1}')

        print(f"\n{compound_name}:")
        print(f"  SMILES: {row['smiles']}")

        print(f"  多任务毒性预测与应用域状态:")
        for task_name in TASK_NAMES:
            pred_label = row.get(f'{task_name}_label', 'N/A')
            pred_prob = row.get(f'{task_name}_probability', 0)

            # 检查该任务的应用域状态
            ad_status_col = f'{task_name}_in_applicability_domain'
            ad_reason_col = f'{task_name}_ad_reason'

            if ad_status_col in row:
                ad_status = "域内" if row[ad_status_col] else "域外"
                ad_reason = row.get(ad_reason_col, "无原因信息")
            else:
                ad_status = "未知"
                ad_reason = "应用域信息缺失"

            if pred_prob > 0:
                print(f"    {task_name}: {pred_label} (概率: {pred_prob:.3f}) - 应用域: {ad_status}")
                if ad_status == "域外":
                    print(f"      └─ 原因: {ad_reason}")

        # 给出综合建议
        if HAS_ADSAL:
            # 统计应用域内的任务数量
            in_domain_tasks = []
            out_domain_tasks = []

            for task_name in TASK_NAMES:
                ad_status_col = f'{task_name}_in_applicability_domain'
                if ad_status_col in row:
                    if row[ad_status_col]:
                        in_domain_tasks.append(task_name)
                    else:
                        out_domain_tasks.append(task_name)

            print(f"  综合建议:")
            if len(in_domain_tasks) == len(TASK_NAMES):
                print(f"    ✅ 所有任务预测结果可信，建议采用")
            elif len(in_domain_tasks) > len(out_domain_tasks):
                print(f"    ⚠️ 大部分任务({len(in_domain_tasks)}/{len(TASK_NAMES)})预测可信")
                print(f"    ⚠️ 以下任务需要实验验证: {', '.join(out_domain_tasks)}")
            else:
                print(f"    ❌ 多数任务({len(out_domain_tasks)}/{len(TASK_NAMES)})预测可信度低，建议实验验证")
        else:
            print(f"  综合建议: 应用域判断功能不可用，建议谨慎使用预测结果")

    print("=" * 100)

def run_single_prediction_with_ad():
    """运行单个SMILES预测并进行应用域判断"""
    print("=" * 60)
    print("单个SMILES多任务预测与应用域判断")
    print("=" * 60)

    print(f"SMILES: {SINGLE_SMILES}")
    print(f"相似性密度阈值: {OPTIMAL_DENSLB}")
    print(f"局域不连续性阈值: {OPTIMAL_LDUB}")
    print()

    try:
        # 步骤1: 多任务毒性预测
        predictor = MTLScrPredictor(MODEL_PATH)
        prediction_result = predictor.predict_single(SINGLE_SMILES)

        if prediction_result.get('error'):
            print(f"预测失败: {prediction_result['error']}")
            return

        print("步骤1: 多任务毒性预测完成")
        for task_name in TASK_NAMES:
            pred_label = prediction_result.get(f'{task_name}_label', 'N/A')
            pred_prob = prediction_result.get(f'{task_name}_probability', 0)
            print(f"  {task_name}: {pred_label} (概率: {pred_prob:.4f})")

        # 步骤2: 应用域判断
        df_train = load_training_data(TRAINING_DATA_PATH)

        # 创建查询数据
        query_data = {
            'smiles': [prediction_result['canonical_smiles']],
            'compound_name': [f"查询化合物_{SINGLE_SMILES[:10]}"]
        }
        df_query = pd.DataFrame(query_data)

        # 计算应用域指标
        df_with_metrics = calculate_ad_metrics(df_train, df_query)

        # 应用应用域判断标准
        df_final = apply_ad_criteria(df_with_metrics, OPTIMAL_DENSLB, OPTIMAL_LDUB)

        # 添加多任务预测结果
        for task_name in TASK_NAMES:
            df_final[f'{task_name}_prediction'] = prediction_result.get(f'{task_name}_prediction', -1)
            df_final[f'{task_name}_probability'] = prediction_result.get(f'{task_name}_probability', 0.0)
            df_final[f'{task_name}_label'] = prediction_result.get(f'{task_name}_label', 'N/A')

        # 显示结果
        print("\n" + "=" * 60)
        print("完整多任务分析结果:")
        print("=" * 60)

        row = df_final.iloc[0]
        status = "应用域内" if row['in_applicability_domain'] else "应用域外"

        print(f"SMILES: {SINGLE_SMILES}")
        print(f"标准SMILES: {row['smiles']}")

        print(f"\n多任务毒性预测:")
        for task_name in TASK_NAMES:
            pred_label = row.get(f'{task_name}_label', 'N/A')
            pred_prob = row.get(f'{task_name}_probability', 0)
            print(f"  {task_name}: {pred_label} (概率: {pred_prob:.4f})")

        print(f"\n应用域状态: {status}")
        print(f"相似性密度: {row['ad_density_value']:.4f} (阈值: {OPTIMAL_DENSLB})")
        print(f"局域不连续性: {row['ad_ld_value']:.4f} (阈值: {OPTIMAL_LDUB})")
        print(f"判断原因: {row['ad_reason']}")

        # 给出建议
        if row['in_applicability_domain']:
            suggestion = "预测结果可信，建议采用"
        else:
            suggestion = "预测结果可信度较低，建议实验验证"
        print(f"建议: {suggestion}")

        # 保存结果
        output_file = f"single_prediction_result_{SINGLE_SMILES[:10].replace('/', '_')}.xlsx"
        df_final.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")

    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_prediction_pipeline():
    """测试预测管道的基本功能"""
    print("=" * 60)
    print("测试预测管道")
    print("=" * 60)

    # 测试SMILES列表
    test_smiles = [
        'CCO',  # 乙醇 - 简单分子
        'CC(C)O',  # 异丙醇
        'c1ccccc1',  # 苯
        'CCN(CC)CC',  # 三乙胺
        'invalid_smiles'  # 无效SMILES用于测试错误处理
    ]

    try:
        # 检查模型文件是否存在
        if not os.path.exists(MODEL_PATH):
            print(f"❌ 模型文件不存在: {MODEL_PATH}")
            print("请确保模型文件路径正确")
            return False

        print(f"✅ 模型文件存在: {MODEL_PATH}")

        # 初始化预测器
        print("初始化预测器...")
        predictor = MTLScrPredictor(MODEL_PATH)
        print("✅ 预测器初始化成功")

        # 测试单个SMILES预测
        print("\n测试单个SMILES预测:")
        for i, smiles in enumerate(test_smiles):
            print(f"\n测试 {i+1}: {smiles}")
            result = predictor.predict_single(smiles)

            if result.get('error'):
                print(f"  ❌ 预测失败: {result['error']}")
            else:
                print(f"  ✅ 预测成功")
                print(f"  标准SMILES: {result['canonical_smiles']}")
                for task_name in TASK_NAMES:
                    pred_label = result.get(f'{task_name}_label', 'N/A')
                    pred_prob = result.get(f'{task_name}_probability', 0)
                    print(f"    {task_name}: {pred_label} (概率: {pred_prob:.4f})")

        print("\n✅ 预测管道测试完成")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("MTL-scr多任务化学品毒性预测与应用域判断整合分析")
    print("=" * 60)

    print(f"预测模式: {PREDICTION_MODE}")
    print(f"模型: MTL-scr (多任务学习)")
    print(f"任务: {', '.join(TASK_NAMES)}")
    if PREDICTION_MODE == 'file':
        print(f"输入文件: {INPUT_FILE}")
        print(f"输出文件: {OUTPUT_FILE}")
    elif PREDICTION_MODE == 'single':
        print(f"单个SMILES: {SINGLE_SMILES}")
    elif PREDICTION_MODE == 'test':
        print(f"测试模式: 验证预测管道")
    print(f"相似性密度阈值: {OPTIMAL_DENSLB}")
    print(f"局域不连续性阈值: {OPTIMAL_LDUB}")
    print()

    try:
        # 检查应用域判断功能是否可用
        if not HAS_ADSAL:
            print("⚠️ 警告: 应用域判断功能不可用，将只进行毒性预测")

        if PREDICTION_MODE == 'test':
            # 测试模式
            print("运行预测管道测试...")
            success = test_prediction_pipeline()
            if success:
                print("\n✅ 预测管道测试通过！可以进行正式预测。")
            else:
                print("\n❌ 预测管道测试失败！请检查配置。")

        elif PREDICTION_MODE == 'single':
            if HAS_ADSAL:
                run_single_prediction_with_ad()
            else:
                # 只进行毒性预测
                predictor = MTLScrPredictor(MODEL_PATH)
                prediction_result = predictor.predict_single(SINGLE_SMILES)

                if prediction_result.get('error'):
                    print(f"预测失败: {prediction_result['error']}")
                    return

                print("多任务毒性预测结果:")
                for task_name in TASK_NAMES:
                    pred_label = prediction_result.get(f'{task_name}_label', 'N/A')
                    pred_prob = prediction_result.get(f'{task_name}_probability', 0)
                    print(f"  {task_name}: {pred_label} (概率: {pred_prob:.4f})")

        elif PREDICTION_MODE == 'file':
            # 检查输入文件
            if not os.path.exists(INPUT_FILE):
                raise FileNotFoundError(f"输入文件不存在: {INPUT_FILE}")

            # 步骤1: 运行毒性预测
            temp_prediction_file = 'temp_prediction.xlsx'
            run_prediction(INPUT_FILE, MODEL_PATH, temp_prediction_file)

            if HAS_ADSAL:
                # 步骤2&3: 多任务应用域判断
                df_prediction = pd.read_excel(temp_prediction_file)
                df_final = apply_multi_task_ad_criteria(df_prediction, OPTIMAL_DENSLB, OPTIMAL_LDUB)

                # 步骤5: 保存结果
                df_final.to_excel(OUTPUT_FILE, index=False)
                print(f"\n结果已保存到: {OUTPUT_FILE}")

                # 步骤6: 生成摘要
                generate_summary(df_final)
            else:
                # 只保存预测结果
                print(f"\n预测结果已保存到: {temp_prediction_file}")
                print("⚠️ 由于缺少adsal模块，未进行应用域判断")

            # 清理临时文件
            if HAS_ADSAL and os.path.exists(temp_prediction_file):
                os.remove(temp_prediction_file)

            print("\n分析完成！")
        else:
            raise ValueError(f"不支持的预测模式: {PREDICTION_MODE}，支持的模式: 'file', 'single', 'test'")

    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

        # 清理临时文件
        if os.path.exists('temp_prediction.xlsx'):
            os.remove('temp_prediction.xlsx')

        sys.exit(1)

if __name__ == "__main__":
    main()
