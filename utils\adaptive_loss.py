import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional, Tuple


class AdaptiveMultiTaskBalancedLoss(nn.Module):
    """
    自适应多任务平衡损失函数 (Adaptive Multi-Task Balanced Loss, AMTBL)
    
    创新点：
    1. 动态类别平衡：使用Focal Loss思想处理正负样本不平衡
    2. 任务重要性自适应：基于数据量和学习难度动态调整任务权重
    3. 温度调节机制：根据训练进度调整损失函数的敏感度
    4. 梯度平衡：确保不同任务的梯度贡献相对平衡
    """
    
    def __init__(self, 
                 classification_num: int,
                 regression_num: int = 0,
                 alpha: float = 0.25,           # Focal Loss中的类别平衡参数
                 gamma: float = 2.0,            # Focal Loss中的难样本聚焦参数
                 beta: float = 0.9999,          # 任务权重更新的动量参数
                 temperature_init: float = 1.0,  # 初始温度参数
                 temperature_decay: float = 0.99, # 温度衰减率
                 gradient_clip: float = 1.0,     # 梯度裁剪阈值
                 device: str = 'cuda'):
        super(AdaptiveMultiTaskBalancedLoss, self).__init__()
        
        self.classification_num = classification_num
        self.regression_num = regression_num
        self.total_tasks = classification_num + regression_num
        self.alpha = alpha
        self.gamma = gamma
        self.beta = beta
        self.temperature_init = temperature_init
        self.temperature_decay = temperature_decay
        self.gradient_clip = gradient_clip
        self.device = device
        
        # 初始化任务权重（均匀分布）
        self.register_buffer('task_weights', torch.ones(self.total_tasks, device=device))
        
        # 记录每个任务的统计信息
        self.register_buffer('task_sample_counts', torch.zeros(self.total_tasks, device=device))
        self.register_buffer('task_loss_history', torch.zeros(self.total_tasks, device=device))
        self.register_buffer('task_difficulty', torch.ones(self.total_tasks, device=device))
        
        # 温度参数
        self.register_buffer('temperature', torch.tensor(temperature_init, device=device))
        
        # 训练步数计数器
        self.register_buffer('step_count', torch.tensor(0, device=device))
        
    def focal_loss(self, logits: torch.Tensor, targets: torch.Tensor, 
                   mask: torch.Tensor, alpha: float = None, gamma: float = None) -> torch.Tensor:
        """
        改进的Focal Loss，专门处理类别不平衡
        """
        if alpha is None:
            alpha = self.alpha
        if gamma is None:
            gamma = self.gamma
            
        # 计算BCE loss
        bce_loss = F.binary_cross_entropy_with_logits(logits, targets, reduction='none')
        
        # 计算概率
        probs = torch.sigmoid(logits)
        
        # 计算pt (正确预测的概率)
        pt = torch.where(targets == 1, probs, 1 - probs)
        
        # 计算alpha权重
        alpha_t = torch.where(targets == 1, alpha, 1 - alpha)
        
        # 计算focal weight
        focal_weight = alpha_t * (1 - pt) ** gamma
        
        # 应用mask和focal weight
        focal_loss = focal_weight * bce_loss * (mask != 0).float()
        
        return focal_loss
    
    def compute_task_difficulty(self, losses: torch.Tensor) -> torch.Tensor:
        """
        基于损失值计算任务难度
        难度高的任务应该获得更多关注
        """
        # 使用指数移动平均更新任务难度
        current_difficulty = losses.detach()
        self.task_difficulty = self.beta * self.task_difficulty + (1 - self.beta) * current_difficulty
        
        # 归一化难度分数
        normalized_difficulty = F.softmax(self.task_difficulty / self.temperature, dim=0)
        
        return normalized_difficulty
    
    def compute_sample_weights(self, mask: torch.Tensor) -> torch.Tensor:
        """
        基于样本数量计算任务权重
        样本少的任务应该获得更高权重
        """
        # 统计每个任务的有效样本数
        current_counts = torch.sum(mask != 0, dim=0).float()
        self.task_sample_counts = self.beta * self.task_sample_counts + (1 - self.beta) * current_counts
        
        # 计算逆频率权重
        total_samples = torch.sum(self.task_sample_counts)
        sample_weights = total_samples / (self.task_sample_counts + 1e-8)
        
        # 归一化
        sample_weights = sample_weights / torch.sum(sample_weights) * self.total_tasks
        
        return sample_weights
    
    def update_temperature(self):
        """
        更新温度参数，随着训练进行逐渐降低
        """
        self.temperature = self.temperature * self.temperature_decay
        self.step_count += 1
    
    def compute_class_balance_weights(self, targets: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """
        动态计算每个任务的类别平衡权重
        """
        class_weights = torch.ones(self.classification_num, device=self.device)

        for task_idx in range(self.classification_num):
            valid_mask = mask[:, task_idx] != 0
            if torch.sum(valid_mask) == 0:
                continue

            valid_targets = targets[valid_mask, task_idx]
            pos_count = torch.sum(valid_targets == 1).float()
            neg_count = torch.sum(valid_targets == 0).float()

            if pos_count > 0 and neg_count > 0:
                # 计算平衡权重
                total = pos_count + neg_count
                pos_weight = total / (2.0 * pos_count)
                neg_weight = total / (2.0 * neg_count)
                # 使用几何平均作为该任务的整体权重
                class_weights[task_idx] = torch.sqrt(pos_weight * neg_weight)

        return class_weights

    def forward(self,
                logits: torch.Tensor,
                targets: torch.Tensor,
                mask: torch.Tensor,
                epoch: int = 0) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播计算损失
        
        Args:
            logits: 模型预测输出 [batch_size, num_tasks]
            targets: 真实标签 [batch_size, num_tasks]
            mask: 有效样本掩码 [batch_size, num_tasks]
            epoch: 当前训练轮数
            
        Returns:
            total_loss: 总损失
            loss_info: 损失详细信息字典
        """
        batch_size = logits.size(0)
        device = logits.device
        
        # 分离分类和回归任务
        if self.classification_num > 0:
            logits_c = logits[:, :self.classification_num]
            targets_c = targets[:, :self.classification_num]
            mask_c = mask[:, :self.classification_num]
        
        if self.regression_num > 0:
            logits_r = logits[:, self.classification_num:]
            targets_r = targets[:, self.classification_num:]
            mask_r = mask[:, self.classification_num:]
        
        # 计算各任务的损失
        task_losses = torch.zeros(self.total_tasks, device=device)
        
        # 分类任务损失（使用Focal Loss）
        if self.classification_num > 0:
            for i in range(self.classification_num):
                focal_loss = self.focal_loss(
                    logits_c[:, i:i+1], 
                    targets_c[:, i:i+1], 
                    mask_c[:, i:i+1]
                )
                task_losses[i] = torch.mean(focal_loss)
        
        # 回归任务损失（使用Huber Loss，对异常值更鲁棒）
        if self.regression_num > 0:
            for i in range(self.regression_num):
                task_idx = self.classification_num + i
                huber_loss = F.smooth_l1_loss(
                    logits_r[:, i], 
                    targets_r[:, i], 
                    reduction='none'
                )
                masked_loss = huber_loss * (mask_r[:, i] != 0).float()
                task_losses[task_idx] = torch.mean(masked_loss)
        
        # 计算自适应权重
        difficulty_weights = self.compute_task_difficulty(task_losses)
        sample_weights = self.compute_sample_weights(mask)
        
        # 组合权重（难度权重 + 样本权重）
        combined_weights = 0.6 * difficulty_weights + 0.4 * sample_weights
        
        # 应用温度调节
        adjusted_weights = F.softmax(combined_weights / self.temperature, dim=0)
        
        # 计算加权总损失
        weighted_losses = task_losses * adjusted_weights
        total_loss = torch.sum(weighted_losses)
        
        # 梯度裁剪（防止梯度爆炸）
        if self.gradient_clip > 0:
            total_loss = torch.clamp(total_loss, max=self.gradient_clip)
        
        # 更新温度
        self.update_temperature()
        
        # 准备返回信息
        loss_info = {
            'total_loss': total_loss,
            'task_losses': task_losses,
            'task_weights': adjusted_weights,
            'difficulty_weights': difficulty_weights,
            'sample_weights': sample_weights,
            'temperature': self.temperature,
            'step_count': self.step_count
        }
        
        return total_loss, loss_info
    
    def get_task_statistics(self) -> Dict[str, torch.Tensor]:
        """
        获取任务统计信息
        """
        return {
            'task_weights': self.task_weights,
            'task_sample_counts': self.task_sample_counts,
            'task_difficulty': self.task_difficulty,
            'temperature': self.temperature
        }


class DynamicTaskWeightScheduler:
    """
    动态任务权重调度器
    根据训练进度和验证性能动态调整任务权重
    """
    
    def __init__(self, num_tasks: int, patience: int = 10, factor: float = 0.8):
        self.num_tasks = num_tasks
        self.patience = patience
        self.factor = factor
        self.best_scores = torch.zeros(num_tasks)
        self.wait_counts = torch.zeros(num_tasks)
        
    def step(self, current_scores: torch.Tensor, task_weights: torch.Tensor) -> torch.Tensor:
        """
        根据当前性能调整任务权重
        """
        improved_mask = current_scores > self.best_scores
        
        # 更新最佳分数和等待计数
        self.best_scores = torch.where(improved_mask, current_scores, self.best_scores)
        self.wait_counts = torch.where(improved_mask, 
                                     torch.zeros_like(self.wait_counts),
                                     self.wait_counts + 1)
        
        # 对长时间没有改善的任务增加权重
        stagnant_mask = self.wait_counts >= self.patience
        adjusted_weights = torch.where(stagnant_mask,
                                     task_weights * (1 / self.factor),
                                     task_weights)
        
        # 归一化权重
        adjusted_weights = adjusted_weights / torch.sum(adjusted_weights) * self.num_tasks
        
        return adjusted_weights


class SimpleWeightedLoss(nn.Module):
    """
    简单加权损失函数 - 自动计算正负样本权重
    无需手动调参，根据数据自动平衡
    """

    def __init__(self, device: str = 'cuda'):
        super(SimpleWeightedLoss, self).__init__()
        self.device = device

    def forward(self, logits: torch.Tensor, targets: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """
        自动加权的BCE损失
        """
        # 计算有效样本的正负比例
        valid_mask = (mask != 0).float()
        valid_targets = targets * valid_mask

        # 统计正负样本数量
        pos_count = torch.sum(valid_targets == 1).float()
        neg_count = torch.sum((valid_targets == 0) * valid_mask).float()
        total_count = pos_count + neg_count

        if total_count == 0:
            return torch.tensor(0.0, device=self.device)

        # 计算权重：让正负样本对损失的贡献相等
        pos_weight = total_count / (2.0 * pos_count + 1e-8)
        neg_weight = total_count / (2.0 * neg_count + 1e-8)

        # 计算BCE损失
        bce_loss = F.binary_cross_entropy_with_logits(logits, targets, reduction='none')

        # 应用权重
        weights = torch.where(targets == 1, pos_weight, neg_weight)
        weighted_loss = bce_loss * weights * valid_mask

        return torch.mean(weighted_loss)


class MultiTaskWeightedLoss(nn.Module):
    """
    多任务加权损失函数
    1. 自动处理每个任务内部的正负样本不平衡
    2. 自动处理任务间的数据量不平衡
    """

    def __init__(self, num_tasks: int, device: str = 'cuda'):
        super(MultiTaskWeightedLoss, self).__init__()
        self.num_tasks = num_tasks
        self.device = device

    def forward(self, logits: torch.Tensor, targets: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """
        多任务加权损失计算
        """
        total_loss = 0.0
        task_losses = []
        task_sample_counts = []

        # 1. 计算每个任务的加权损失和样本数
        for task_idx in range(self.num_tasks):
            task_logits = logits[:, task_idx]
            task_targets = targets[:, task_idx]
            task_mask = mask[:, task_idx]

            # 计算有效样本
            valid_mask = (task_mask != 0).float()
            valid_targets = task_targets * valid_mask

            # 统计正负样本
            pos_count = torch.sum(valid_targets == 1).float()
            neg_count = torch.sum((valid_targets == 0) * valid_mask).float()
            total_count = pos_count + neg_count

            if total_count == 0:
                task_losses.append(0.0)
                task_sample_counts.append(0.0)
                continue

            # 计算类别权重
            pos_weight = total_count / (2.0 * pos_count + 1e-8)
            neg_weight = total_count / (2.0 * neg_count + 1e-8)

            # 计算加权BCE损失
            bce_loss = F.binary_cross_entropy_with_logits(task_logits, task_targets, reduction='none')
            weights = torch.where(task_targets == 1, pos_weight, neg_weight)
            weighted_loss = bce_loss * weights * valid_mask
            task_loss = torch.mean(weighted_loss)

            task_losses.append(task_loss)
            task_sample_counts.append(total_count.item())

        # 2. 计算任务权重（基于数据量的逆权重）
        if sum(task_sample_counts) == 0:
            return torch.tensor(0.0, device=self.device)

        # 数据量少的任务获得更高权重
        total_samples = sum(task_sample_counts)
        task_weights = []
        for count in task_sample_counts:
            if count == 0:
                task_weights.append(0.0)
            else:
                # 逆频率权重：样本少的任务权重高
                weight = total_samples / (self.num_tasks * count)
                task_weights.append(weight)

        # 3. 计算最终加权损失
        for task_idx in range(self.num_tasks):
            if task_sample_counts[task_idx] > 0:
                total_loss += task_losses[task_idx] * task_weights[task_idx]

        return total_loss / self.num_tasks


def run_a_train_epoch_with_simple_weighted_loss(args, epoch, model, data_loader, loss_fn, optimizer):
    """
    使用简单加权损失函数的训练函数
    """
    model.train()
    epoch_loss = 0.0

    for batch_id, batch_data in enumerate(data_loader):
        _, bg, labels, mask = batch_data

        # 数据预处理
        labels = labels.float().to(args['device'])
        mask = mask.float().to(args['device'])
        atom_feats = bg.ndata.pop(args['atom_data_field']).float().to(args['device'])
        bond_feats = bg.edata.pop(args['bond_data_field']).long().to(args['device'])

        # 前向传播
        logits = model(bg, atom_feats, bond_feats)

        # 计算加权损失
        total_loss = loss_fn(logits, labels, mask)

        # 反向传播
        optimizer.zero_grad()
        total_loss.backward()

        # 梯度裁剪（可选）
        if hasattr(args, 'gradient_clip_norm') and args['gradient_clip_norm'] > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), args['gradient_clip_norm'])

        optimizer.step()

        # 累积损失
        epoch_loss += total_loss.item()

        # 清理内存
        del bg, mask, labels, atom_feats, bond_feats, total_loss, logits
        torch.cuda.empty_cache()

    # 计算平均损失
    avg_epoch_loss = epoch_loss / len(data_loader)

    # 打印信息
    if epoch % 10 == 0:
        print(f'Epoch {epoch}: Avg Weighted Loss = {avg_epoch_loss:.4f}')

    return avg_epoch_loss


def run_a_train_epoch_with_adaptive_loss(args, epoch, model, data_loader, adaptive_loss_fn, optimizer):
    """
    使用自适应多任务平衡损失函数的训练函数
    """
    model.train()
    epoch_loss = 0.0
    epoch_loss_info = {
        'task_losses': torch.zeros(adaptive_loss_fn.total_tasks, device=args['device']),
        'task_weights': torch.zeros(adaptive_loss_fn.total_tasks, device=args['device']),
        'batch_count': 0
    }

    for batch_id, batch_data in enumerate(data_loader):
        smiles, bg, labels, mask = batch_data

        # 数据预处理
        labels = labels.float().to(args['device'])
        mask = mask.float().to(args['device'])
        atom_feats = bg.ndata.pop(args['atom_data_field']).float().to(args['device'])
        bond_feats = bg.edata.pop(args['bond_data_field']).long().to(args['device'])

        # 前向传播
        logits = model(bg, atom_feats, bond_feats)

        # 计算自适应损失
        total_loss, loss_info = adaptive_loss_fn(logits, labels, mask, epoch)

        # 反向传播
        optimizer.zero_grad()
        total_loss.backward()

        # 梯度裁剪（可选）
        if hasattr(args, 'gradient_clip_norm') and args['gradient_clip_norm'] > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), args['gradient_clip_norm'])

        optimizer.step()

        # 累积损失信息
        epoch_loss += total_loss.item()
        epoch_loss_info['task_losses'] += loss_info['task_losses'].detach()
        epoch_loss_info['task_weights'] += loss_info['task_weights'].detach()
        epoch_loss_info['batch_count'] += 1

        # 清理内存
        del bg, mask, labels, atom_feats, bond_feats, total_loss, logits
        torch.cuda.empty_cache()

    # 计算平均值
    avg_epoch_loss = epoch_loss / len(data_loader)
    epoch_loss_info['task_losses'] /= epoch_loss_info['batch_count']
    epoch_loss_info['task_weights'] /= epoch_loss_info['batch_count']

    # 打印详细信息
    if epoch % 10 == 0:  # 每10个epoch打印一次详细信息
        print(f'Epoch {epoch}: Avg Loss = {avg_epoch_loss:.4f}')
        print(f'Task Losses: {epoch_loss_info["task_losses"].cpu().numpy()}')
        print(f'Task Weights: {epoch_loss_info["task_weights"].cpu().numpy()}')
        print(f'Temperature: {adaptive_loss_fn.temperature.item():.4f}')

    return avg_epoch_loss, epoch_loss_info
