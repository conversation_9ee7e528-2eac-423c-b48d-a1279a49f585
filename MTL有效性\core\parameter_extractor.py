"""
参数提取器模块

从MGA模型中提取TFP-G相关参数，包括注意力权重和全连接层参数
"""

import torch
import numpy as np
import os
from utils.MY_GNN import MGA
import warnings
warnings.filterwarnings('ignore')


class ParameterExtractor:
    """TFP-G参数提取器"""
    
    def __init__(self, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
    
    def load_model_from_checkpoint(self, model_path, model_config):
        """从checkpoint加载模型"""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        model = MGA(**model_config)
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
            
        model.to(self.device)
        model.eval()
        return model
    
    def extract_tfp_parameters(self, model, model_name):
        """提取TFP-G相关参数"""
        tfp_params = {}

        # 提取GNN层参数
        for i, gnn_layer in enumerate(model.gnn_layers):
            for name, param in gnn_layer.named_parameters():
                key = f"gnn_layer_{i}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()

        # 提取注意力权重参数 (WeightAndSum中的atom_weighting_specific)
        for i, attention_layer in enumerate(model.weighted_sum_readout.atom_weighting_specific):
            for name, param in attention_layer.named_parameters():
                key = f"attention_task_{i}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()

        # 提取共享注意力权重参数
        for name, param in model.weighted_sum_readout.shared_weighting.named_parameters():
            key = f"shared_attention_{name}"
            tfp_params[key] = param.detach().cpu().numpy().flatten()

        # 提取全连接层参数
        for i in range(model.task_num):
            # FC层1 - 检查是否存在且不是Identity层
            if (hasattr(model, 'fc_layers1') and len(model.fc_layers1) > i and
                not isinstance(model.fc_layers1[i], torch.nn.Identity)):
                for name, param in model.fc_layers1[i].named_parameters():
                    key = f"fc1_task_{i}_{name}"
                    tfp_params[key] = param.detach().cpu().numpy().flatten()

            # FC层2 - 检查是否存在且不是Identity层
            if (hasattr(model, 'fc_layers2') and len(model.fc_layers2) > i and
                not isinstance(model.fc_layers2[i], torch.nn.Identity)):
                for name, param in model.fc_layers2[i].named_parameters():
                    key = f"fc2_task_{i}_{name}"
                    tfp_params[key] = param.detach().cpu().numpy().flatten()

            # FC层3 - 检查是否存在且不是Identity层
            if (hasattr(model, 'fc_layers3') and len(model.fc_layers3) > i and
                not isinstance(model.fc_layers3[i], torch.nn.Identity)):
                for name, param in model.fc_layers3[i].named_parameters():
                    key = f"fc3_task_{i}_{name}"
                    tfp_params[key] = param.detach().cpu().numpy().flatten()

            # 输出层
            if hasattr(model, 'output_layer1') and len(model.output_layer1) > i:
                for name, param in model.output_layer1[i].named_parameters():
                    key = f"output_task_{i}_{name}"
                    tfp_params[key] = param.detach().cpu().numpy().flatten()

        return tfp_params

    def extract_task_specific_parameters(self, model, model_name, target_task_name):
        """提取多任务模型中特定任务的参数"""
        tfp_params = {}

        # 任务名称到索引的映射
        task_mapping = {
            'FishLC50': 0,
            'FishEL_NOEC': 1,
            'DMRepNOEC': 2,
            'DMImbEC50': 3,
            'AlaGroErC50': 4
        }

        target_task_index = task_mapping.get(target_task_name, 0)

        # 提取GNN层参数（共享的）
        for i, gnn_layer in enumerate(model.gnn_layers):
            for name, param in gnn_layer.named_parameters():
                key = f"gnn_layer_{i}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()

        # 提取共享注意力权重参数
        for name, param in model.weighted_sum_readout.shared_weighting.named_parameters():
            key = f"shared_attention_{name}"
            tfp_params[key] = param.detach().cpu().numpy().flatten()

        # 提取目标任务特定的注意力权重参数
        if len(model.weighted_sum_readout.atom_weighting_specific) > target_task_index:
            attention_layer = model.weighted_sum_readout.atom_weighting_specific[target_task_index]
            for name, param in attention_layer.named_parameters():
                key = f"attention_task_{target_task_index}_{name}"
                tfp_params[key] = param.detach().cpu().numpy().flatten()

        # 提取目标任务特定的全连接层参数
        for fc_layer_name in ['fc_layers1', 'fc_layers2', 'fc_layers3', 'output_layer1']:
            if hasattr(model, fc_layer_name):
                fc_layers = getattr(model, fc_layer_name)
                if len(fc_layers) > target_task_index:
                    fc_layer = fc_layers[target_task_index]
                    if not isinstance(fc_layer, torch.nn.Identity):
                        for name, param in fc_layer.named_parameters():
                            key = f"{fc_layer_name}_{target_task_index}_{name}"
                            tfp_params[key] = param.detach().cpu().numpy().flatten()

        return tfp_params

    def extract_attention_parameters(self, model, model_name):
        """专门提取注意力权重参数"""
        attention_params = {}
        
        # 提取任务特定注意力权重
        for i, attention_layer in enumerate(model.weighted_sum_readout.atom_weighting_specific):
            for name, param in attention_layer.named_parameters():
                key = f"attention_task_{i}_{name}"
                attention_params[key] = param.detach().cpu().numpy().flatten()
        
        # 提取共享注意力权重
        for name, param in model.weighted_sum_readout.shared_weighting.named_parameters():
            key = f"shared_attention_{name}"
            attention_params[key] = param.detach().cpu().numpy().flatten()
        
        return attention_params
    
    def extract_fc_parameters(self, model, model_name):
        """专门提取全连接层参数"""
        fc_params = {}

        print(f"    调试FC参数提取 - {model_name}:")
        print(f"      model.task_num: {model.task_num}")
        print(f"      hasattr fc_layers1: {hasattr(model, 'fc_layers1')}")
        print(f"      hasattr fc_layers2: {hasattr(model, 'fc_layers2')}")
        print(f"      hasattr fc_layers3: {hasattr(model, 'fc_layers3')}")
        print(f"      hasattr output_layer1: {hasattr(model, 'output_layer1')}")

        for i in range(model.task_num):
            print(f"      处理任务 {i}:")

            # FC层1 - 检查是否存在且不是Identity层
            if (hasattr(model, 'fc_layers1') and len(model.fc_layers1) > i and
                not isinstance(model.fc_layers1[i], torch.nn.Identity)):
                for name, param in model.fc_layers1[i].named_parameters():
                    key = f"fc1_task_{i}_{name}"
                    fc_params[key] = param.detach().cpu().numpy().flatten()
                    print(f"        添加 {key}: {param.shape} -> {len(fc_params[key])}")

            # FC层2 - 检查是否存在且不是Identity层
            if (hasattr(model, 'fc_layers2') and len(model.fc_layers2) > i and
                not isinstance(model.fc_layers2[i], torch.nn.Identity)):
                for name, param in model.fc_layers2[i].named_parameters():
                    key = f"fc2_task_{i}_{name}"
                    fc_params[key] = param.detach().cpu().numpy().flatten()
                    print(f"        添加 {key}: {param.shape} -> {len(fc_params[key])}")

            # FC层3 - 检查是否存在且不是Identity层
            if (hasattr(model, 'fc_layers3') and len(model.fc_layers3) > i and
                not isinstance(model.fc_layers3[i], torch.nn.Identity)):
                for name, param in model.fc_layers3[i].named_parameters():
                    key = f"fc3_task_{i}_{name}"
                    fc_params[key] = param.detach().cpu().numpy().flatten()
                    print(f"        添加 {key}: {param.shape} -> {len(fc_params[key])}")

            # 输出层
            if hasattr(model, 'output_layer1') and len(model.output_layer1) > i:
                for name, param in model.output_layer1[i].named_parameters():
                    key = f"output_task_{i}_{name}"
                    fc_params[key] = param.detach().cpu().numpy().flatten()
                    print(f"        添加 {key}: {param.shape} -> {len(fc_params[key])}")

        print(f"      最终提取到 {len(fc_params)} 个FC参数")
        return fc_params
    
    def extract_from_state_dict(self, state_dict, model_name):
        """从state_dict中提取TFP-G相关参数"""
        tfp_params = {}
        
        # 提取注意力权重参数
        for key, param in state_dict.items():
            if 'weighted_sum_readout.atom_weighting_specific' in key:
                tfp_params[key] = param.cpu().numpy().flatten()
            elif 'weighted_sum_readout.shared_weighting' in key:
                tfp_params[key] = param.cpu().numpy().flatten()
            elif any(fc_layer in key for fc_layer in ['fc_layers1', 'fc_layers2', 'fc_layers3', 'output_layer1']):
                tfp_params[key] = param.cpu().numpy().flatten()
        
        return tfp_params
