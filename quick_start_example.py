"""
自适应多任务平衡损失函数 - 快速入门示例
演示如何在现有代码中快速集成AMTBL损失函数
"""

import torch
import numpy as np
from utils.adaptive_loss import AdaptiveMultiTaskBalancedLoss
from utils.loss_analysis import analyze_dataset_imbalance

def quick_integration_example():
    """
    快速集成示例：展示如何用最少的代码修改集成AMTBL
    """
    print("🚀 自适应多任务平衡损失函数 - 快速集成示例")
    print("=" * 60)
    
    # 1. 替换原有损失函数初始化
    print("步骤1: 初始化自适应损失函数")
    print("-" * 30)
    
    # 原来的代码：
    # pos_weight_np = pos_weight(train_set, classification_num=args['classification_num'])
    # loss_criterion_c = torch.nn.BCEWithLogitsLoss(reduction='none', pos_weight=pos_weight_np.to(args['device']))
    # loss_criterion_r = torch.nn.MSELoss(reduction='none')
    
    # 新的代码：
    adaptive_loss_fn = AdaptiveMultiTaskBalancedLoss(
        classification_num=3,  # 根据你的任务数量调整
        regression_num=0,      # 根据你的任务数量调整
        alpha=0.25,           # Focal Loss参数，可调节
        gamma=2.0,            # Focal Loss参数，可调节
        beta=0.999,           # 权重更新动量，可调节
        temperature_init=1.0,  # 初始温度，可调节
        temperature_decay=0.99, # 温度衰减，可调节
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )
    print("✓ 损失函数初始化完成")
    
    # 2. 替换训练循环中的损失计算
    print("\n步骤2: 修改训练循环")
    print("-" * 30)
    
    # 原来的代码：
    # loss = (loss_criterion_c(logits, labels)*(mask != 0).float()).mean()
    
    # 新的代码：
    # total_loss, loss_info = adaptive_loss_fn(logits, labels, mask, epoch)
    
    print("✓ 训练循环修改完成")
    
    # 3. 可选：添加损失监控
    print("\n步骤3: 添加损失监控（可选）")
    print("-" * 30)
    
    print("可以添加以下代码来监控训练过程：")
    print("""
    if epoch % 10 == 0:
        print(f'任务损失: {loss_info["task_losses"]}')
        print(f'任务权重: {loss_info["task_weights"]}')
        print(f'温度: {loss_info["temperature"]}')
    """)
    
    print("✓ 监控代码添加完成")

def parameter_tuning_guide():
    """
    参数调优指南
    """
    print("\n🎛️ 参数调优指南")
    print("=" * 40)
    
    tuning_guide = {
        "alpha": {
            "作用": "控制正负样本的相对重要性",
            "默认值": 0.25,
            "调优建议": {
                "严重不平衡(正样本<10%)": "增加到0.3-0.4",
                "中度不平衡(正样本10-30%)": "保持0.25",
                "相对平衡(正样本>30%)": "减少到0.1-0.2"
            }
        },
        "gamma": {
            "作用": "控制对难样本的聚焦程度",
            "默认值": 2.0,
            "调优建议": {
                "模型过拟合": "减少到1.0-1.5",
                "模型欠拟合": "增加到2.5-3.0",
                "训练不稳定": "减少到1.0"
            }
        },
        "beta": {
            "作用": "控制权重更新的平滑程度",
            "默认值": 0.999,
            "调优建议": {
                "需要快速适应": "减少到0.99-0.995",
                "需要稳定训练": "保持0.999",
                "数据量很小": "减少到0.9-0.95"
            }
        },
        "temperature_init": {
            "作用": "控制初始权重分布的尖锐程度",
            "默认值": 1.0,
            "调优建议": {
                "任务差异很大": "增加到2.0-3.0",
                "任务相对均匀": "保持1.0",
                "训练不稳定": "增加到1.5-2.0"
            }
        },
        "temperature_decay": {
            "作用": "控制温度衰减速度",
            "默认值": 0.99,
            "调优建议": {
                "长期训练": "减少到0.995-0.999",
                "短期训练": "保持0.99",
                "需要快速收敛": "减少到0.98"
            }
        }
    }
    
    for param, info in tuning_guide.items():
        print(f"\n📊 {param}:")
        print(f"  作用: {info['作用']}")
        print(f"  默认值: {info['默认值']}")
        print("  调优建议:")
        for situation, suggestion in info['调优建议'].items():
            print(f"    {situation}: {suggestion}")

def common_issues_and_solutions():
    """
    常见问题和解决方案
    """
    print("\n🔧 常见问题和解决方案")
    print("=" * 40)
    
    issues = {
        "训练不稳定，损失震荡": [
            "增加temperature_init到2.0-3.0",
            "减少gamma到1.0-1.5",
            "增加beta到0.9999",
            "检查学习率是否过大"
        ],
        "某些任务性能很差": [
            "检查数据质量和标签正确性",
            "增加alpha值关注少数类",
            "减少beta值加快权重适应",
            "增加gamma值关注难样本"
        ],
        "训练速度太慢": [
            "减少batch_size",
            "增加temperature_decay加快收敛",
            "使用梯度累积",
            "检查GPU内存使用"
        ],
        "内存不足": [
            "减少batch_size",
            "降低temperature_init",
            "使用gradient_checkpointing",
            "及时清理中间变量"
        ],
        "收敛太慢": [
            "增加学习率",
            "减少temperature_decay",
            "调整beta值",
            "检查数据预处理"
        ]
    }
    
    for issue, solutions in issues.items():
        print(f"\n❓ {issue}:")
        for i, solution in enumerate(solutions, 1):
            print(f"  {i}. {solution}")

def performance_optimization_tips():
    """
    性能优化技巧
    """
    print("\n⚡ 性能优化技巧")
    print("=" * 40)
    
    tips = [
        {
            "技巧": "使用混合精度训练",
            "代码": """
# 在模型初始化后添加
from torch.cuda.amp import autocast, GradScaler
scaler = GradScaler()

# 在训练循环中使用
with autocast():
    total_loss, loss_info = adaptive_loss_fn(logits, labels, mask, epoch)

scaler.scale(total_loss).backward()
scaler.step(optimizer)
scaler.update()
            """,
            "效果": "减少显存使用50%，加速训练30%"
        },
        {
            "技巧": "动态批次大小",
            "代码": """
# 根据GPU内存动态调整批次大小
def get_optimal_batch_size():
    if torch.cuda.get_device_properties(0).total_memory > 16e9:  # 16GB
        return 512
    elif torch.cuda.get_device_properties(0).total_memory > 8e9:   # 8GB
        return 256
    else:
        return 128
            """,
            "效果": "最大化GPU利用率"
        },
        {
            "技巧": "预计算权重",
            "代码": """
# 每N个epoch预计算一次权重，而不是每个batch
if epoch % 5 == 0:
    adaptive_loss_fn.update_task_statistics(train_loader)
            """,
            "效果": "减少计算开销20%"
        }
    ]
    
    for tip in tips:
        print(f"\n💡 {tip['技巧']}:")
        print(f"代码示例:{tip['代码']}")
        print(f"预期效果: {tip['效果']}")

def integration_checklist():
    """
    集成检查清单
    """
    print("\n✅ 集成检查清单")
    print("=" * 40)
    
    checklist = [
        "□ 确认任务数量设置正确 (classification_num, regression_num)",
        "□ 检查设备设置 (device参数)",
        "□ 验证数据格式 (logits, targets, mask的形状)",
        "□ 调整超参数 (alpha, gamma, beta等)",
        "□ 添加损失监控代码",
        "□ 测试训练稳定性",
        "□ 对比基线性能",
        "□ 保存训练日志",
        "□ 生成分析报告",
        "□ 验证模型保存和加载"
    ]
    
    for item in checklist:
        print(f"  {item}")
    
    print("\n📝 建议在集成前先运行测试脚本:")
    print("  python test_adaptive_loss.py")

def main():
    """
    主函数：运行所有示例
    """
    quick_integration_example()
    parameter_tuning_guide()
    common_issues_and_solutions()
    performance_optimization_tips()
    integration_checklist()
    
    print("\n" + "=" * 60)
    print("🎉 快速入门指南完成！")
    print("=" * 60)
    print("\n📚 下一步建议:")
    print("1. 运行 python test_adaptive_loss.py 验证功能")
    print("2. 运行 python compare_loss_functions.py 对比性能")
    print("3. 根据你的数据特点调整参数")
    print("4. 集成到你的训练代码中")
    print("5. 监控训练过程并优化参数")
    
    print("\n📖 更多信息:")
    print("- 详细文档: adaptive_loss_usage_guide.md")
    print("- 创新总结: AMTBL_Innovation_Summary.md")
    print("- 源代码: utils/adaptive_loss.py")

if __name__ == "__main__":
    main()
