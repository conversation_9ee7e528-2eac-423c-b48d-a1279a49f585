"""
相似性计算器模块

提供多种参数相似性计算方法
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')


class SimilarityCalculator:
    """参数相似性计算器"""
    
    def __init__(self):
        pass
    
    def compute_similarity(self, params1, params2, method='cosine'):
        """
        计算两组参数之间的相似性

        Args:
            params1 (dict or np.ndarray): 第一组参数
            params2 (dict or np.ndarray): 第二组参数
            method (str): 相似性计算方法 ('cosine', 'correlation', 'euclidean')

        Returns:
            float: 相似性值 [0, 1]
        """
        # 处理不同类型的输入
        if isinstance(params1, dict) and isinstance(params2, dict):
            # 将参数字典转换为向量
            common_keys = set(params1.keys()) & set(params2.keys())

            if not common_keys:
                return 0.0

            vec1 = np.concatenate([params1[key] for key in sorted(common_keys)])
            vec2 = np.concatenate([params2[key] for key in sorted(common_keys)])
        elif isinstance(params1, np.ndarray) and isinstance(params2, np.ndarray):
            # 直接使用numpy数组
            vec1 = params1.flatten()
            vec2 = params2.flatten()

            # 检查维度是否匹配
            if len(vec1) != len(vec2):
                raise ValueError(f"参数向量维度不匹配: {len(vec1)} vs {len(vec2)}")
        else:
            raise TypeError("参数类型必须都是dict或都是numpy.ndarray")
        
        if method == 'cosine':
            similarity = cosine_similarity([vec1], [vec2])[0, 0]
        elif method == 'correlation':
            correlation_matrix = np.corrcoef(vec1, vec2)
            similarity = correlation_matrix[0, 1] if not np.isnan(correlation_matrix[0, 1]) else 0.0
        elif method == 'euclidean':
            distance = np.linalg.norm(vec1 - vec2)
            similarity = 1 / (1 + distance)
        else:
            raise ValueError(f"未知的相似性方法: {method}")
        
        return similarity
    
    def compute_similarity_matrix(self, params_dict, method='cosine'):
        """
        计算多个参数组之间的相似性矩阵
        
        Args:
            params_dict (dict): 参数字典，键为模型名称，值为参数字典
            method (str): 相似性计算方法
        
        Returns:
            tuple: (相似性矩阵, 模型名称列表)
        """
        model_names = list(params_dict.keys())
        n_models = len(model_names)
        similarity_matrix = np.zeros((n_models, n_models))
        
        for i, name1 in enumerate(model_names):
            for j, name2 in enumerate(model_names):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    similarity = self.compute_similarity(
                        params_dict[name1], params_dict[name2], method
                    )
                    similarity_matrix[i, j] = similarity
        
        return similarity_matrix, model_names
    
    def compute_pairwise_similarities(self, params_dict, method='cosine'):
        """
        计算所有模型对之间的相似性
        
        Args:
            params_dict (dict): 参数字典
            method (str): 相似性计算方法
        
        Returns:
            list: [(模型1, 模型2, 相似性值), ...]
        """
        model_names = list(params_dict.keys())
        similarities = []
        
        for i, name1 in enumerate(model_names):
            for j, name2 in enumerate(model_names):
                if i < j:  # 只计算上三角
                    similarity = self.compute_similarity(
                        params_dict[name1], params_dict[name2], method
                    )
                    similarities.append((name1, name2, similarity))
        
        return similarities
    
    def get_similarity_statistics(self, similarity_matrix):
        """
        计算相似性矩阵的统计信息
        
        Args:
            similarity_matrix (np.ndarray): 相似性矩阵
        
        Returns:
            dict: 统计信息
        """
        # 只考虑上三角（排除对角线）
        upper_triangle = similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)]
        
        stats = {
            'mean': np.mean(upper_triangle),
            'std': np.std(upper_triangle),
            'min': np.min(upper_triangle),
            'max': np.max(upper_triangle),
            'median': np.median(upper_triangle)
        }
        
        return stats
    
    def find_most_similar_pairs(self, params_dict, method='cosine', top_k=5):
        """
        找到最相似的模型对
        
        Args:
            params_dict (dict): 参数字典
            method (str): 相似性计算方法
            top_k (int): 返回前k个最相似的对
        
        Returns:
            list: 最相似的模型对列表
        """
        similarities = self.compute_pairwise_similarities(params_dict, method)
        similarities.sort(key=lambda x: x[2], reverse=True)
        
        return similarities[:top_k]
    
    def find_least_similar_pairs(self, params_dict, method='cosine', top_k=5):
        """
        找到最不相似的模型对
        
        Args:
            params_dict (dict): 参数字典
            method (str): 相似性计算方法
            top_k (int): 返回前k个最不相似的对
        
        Returns:
            list: 最不相似的模型对列表
        """
        similarities = self.compute_pairwise_similarities(params_dict, method)
        similarities.sort(key=lambda x: x[2])
        
        return similarities[:top_k]
