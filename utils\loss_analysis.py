from typing import List, Dict


def print_loss_summary(loss_analysis_results: List[Dict]):
    """
    打印损失函数训练总结
    """
    if not loss_analysis_results:
        print("没有损失分析结果")
        return

    print("\n" + "=" * 50)
    print("损失函数训练总结")
    print("=" * 50)

    for time_id, result in enumerate(loss_analysis_results):
        training_history = result['training_history']

        if not training_history:
            continue

        print(f"\n第 {time_id + 1} 次运行:")
        print(f"  训练轮数: {len(training_history)}")
        print(f"  初始损失: {training_history[0]['avg_loss']:.4f}")
        print(f"  最终损失: {training_history[-1]['avg_loss']:.4f}")
        print(f"  损失下降: {training_history[0]['avg_loss'] - training_history[-1]['avg_loss']:.4f}")

        # 如果有多任务信息
        if 'task_losses' in training_history[-1]:
            final_task_losses = training_history[-1]['task_losses']
            final_task_weights = training_history[-1]['task_weights']
            print(f"  最终任务损失: {final_task_losses}")
            print(f"  最终任务权重: {final_task_weights}")

        # 如果有温度信息
        if 'temperature' in training_history[-1]:
            print(f"  最终温度: {training_history[-1]['temperature']:.4f}")



