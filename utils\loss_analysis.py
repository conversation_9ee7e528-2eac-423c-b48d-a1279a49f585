import torch
import numpy as np
from typing import List, Dict


def analyze_dataset_imbalance(train_set, classification_num: int, task_names: List[str] = None):
    """
    分析数据集中的类别不平衡情况
    """
    if task_names is None:
        task_names = [f'Task_{i}' for i in range(classification_num)]

    # 提取标签和掩码
    labels = np.array([sample[2] for sample in train_set])
    masks = np.array([sample[3] for sample in train_set])

    analysis_results = {}

    print("=" * 60)
    print("数据集不平衡分析报告")
    print("=" * 60)

    for task_idx in range(classification_num):
        task_name = task_names[task_idx] if task_idx < len(task_names) else f'Task_{task_idx}'

        # 获取有效样本
        valid_mask = masks[:, task_idx] == 1
        valid_labels = labels[valid_mask, task_idx]

        if len(valid_labels) == 0:
            print(f"{task_name}: 无有效样本")
            continue

        # 统计正负样本
        pos_count = np.sum(valid_labels == 1)
        neg_count = np.sum(valid_labels == 0)
        total_count = len(valid_labels)

        # 计算不平衡比率
        imbalance_ratio = neg_count / (pos_count + 1e-8)
        pos_ratio = pos_count / total_count

        analysis_results[task_name] = {
            'total_samples': total_count,
            'positive_samples': pos_count,
            'negative_samples': neg_count,
            'positive_ratio': pos_ratio,
            'imbalance_ratio': imbalance_ratio
        }

        print(f"\n{task_name}:")
        print(f"  总样本数: {total_count}")
        print(f"  正样本: {pos_count} ({pos_ratio:.2%})")
        print(f"  负样本: {neg_count} ({1-pos_ratio:.2%})")
        print(f"  不平衡比率 (负/正): {imbalance_ratio:.2f}")

        if imbalance_ratio > 2:
            print(f"  ⚠️  严重不平衡！")
        elif imbalance_ratio > 1.5:
            print(f"  ⚠️  中度不平衡")
        else:
            print(f"  ✓  相对平衡")

    return analysis_results


def print_loss_summary(loss_analysis_results: List[Dict]):
    """
    打印损失函数训练总结
    """
    if not loss_analysis_results:
        print("没有损失分析结果")
        return

    print("\n" + "=" * 50)
    print("损失函数训练总结")
    print("=" * 50)

    for time_id, result in enumerate(loss_analysis_results):
        training_history = result['training_history']

        if not training_history:
            continue

        print(f"\n第 {time_id + 1} 次运行:")
        print(f"  训练轮数: {len(training_history)}")
        print(f"  初始损失: {training_history[0]['avg_loss']:.4f}")
        print(f"  最终损失: {training_history[-1]['avg_loss']:.4f}")
        print(f"  损失下降: {training_history[0]['avg_loss'] - training_history[-1]['avg_loss']:.4f}")

        # 如果有多任务信息
        if 'task_losses' in training_history[-1]:
            final_task_losses = training_history[-1]['task_losses']
            final_task_weights = training_history[-1]['task_weights']
            print(f"  最终任务损失: {final_task_losses}")
            print(f"  最终任务权重: {final_task_weights}")

        # 如果有温度信息
        if 'temperature' in training_history[-1]:
            print(f"  最终温度: {training_history[-1]['temperature']:.4f}")


def generate_loss_analysis_report(loss_analysis_results: List[Dict],
                                 dataset_analysis: Dict,
                                 save_path: str = 'analysis/loss_report.txt'):
    """
    生成简化的损失函数分析报告
    """
    import os
    os.makedirs(os.path.dirname(save_path), exist_ok=True)

    with open(save_path, 'w', encoding='utf-8') as f:
        f.write("损失函数分析报告\n")
        f.write("=" * 30 + "\n\n")

        # 数据集分析
        f.write("1. 数据集不平衡分析\n")
        f.write("-" * 20 + "\n")
        for task_name, stats in dataset_analysis.items():
            f.write(f"{task_name}:\n")
            f.write(f"  总样本数: {stats['total_samples']}\n")
            f.write(f"  正样本比例: {stats['positive_ratio']:.2%}\n")
            f.write(f"  不平衡比率: {stats['imbalance_ratio']:.2f}\n\n")

        # 训练总结
        f.write("2. 训练总结\n")
        f.write("-" * 20 + "\n")
        if loss_analysis_results:
            for i, result in enumerate(loss_analysis_results):
                f.write(f"第{i+1}次运行:\n")
                history = result['training_history']
                if history:
                    f.write(f"  训练轮数: {len(history)}\n")
                    f.write(f"  初始损失: {history[0]['avg_loss']:.4f}\n")
                    f.write(f"  最终损失: {history[-1]['avg_loss']:.4f}\n\n")

    print(f"分析报告已保存到: {save_path}")
