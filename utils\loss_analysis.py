import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any
import os


def analyze_dataset_imbalance(train_set, classification_num: int, task_names: List[str] = None):
    """
    分析数据集中的类别不平衡和任务数据量不平衡情况
    """
    if task_names is None:
        task_names = [f'Task_{i}' for i in range(classification_num)]
    
    # 提取标签和掩码
    labels = np.array([sample[2] for sample in train_set])
    masks = np.array([sample[3] for sample in train_set])
    
    analysis_results = {}
    
    print("=" * 60)
    print("数据集不平衡分析报告")
    print("=" * 60)
    
    for task_idx in range(classification_num):
        task_name = task_names[task_idx] if task_idx < len(task_names) else f'Task_{task_idx}'
        
        # 获取有效样本
        valid_mask = masks[:, task_idx] == 1
        valid_labels = labels[valid_mask, task_idx]
        
        if len(valid_labels) == 0:
            print(f"{task_name}: 无有效样本")
            continue
            
        # 统计正负样本
        pos_count = np.sum(valid_labels == 1)
        neg_count = np.sum(valid_labels == 0)
        total_count = len(valid_labels)
        
        # 计算不平衡比率
        imbalance_ratio = neg_count / (pos_count + 1e-8)
        pos_ratio = pos_count / total_count
        
        analysis_results[task_name] = {
            'total_samples': total_count,
            'positive_samples': pos_count,
            'negative_samples': neg_count,
            'positive_ratio': pos_ratio,
            'imbalance_ratio': imbalance_ratio
        }
        
        print(f"\n{task_name}:")
        print(f"  总样本数: {total_count}")
        print(f"  正样本: {pos_count} ({pos_ratio:.2%})")
        print(f"  负样本: {neg_count} ({1-pos_ratio:.2%})")
        print(f"  不平衡比率 (负/正): {imbalance_ratio:.2f}")
        
        if imbalance_ratio > 2:
            print(f"  ⚠️  严重不平衡！")
        elif imbalance_ratio > 1.5:
            print(f"  ⚠️  中度不平衡")
        else:
            print(f"  ✓  相对平衡")
    
    return analysis_results


def visualize_loss_evolution(loss_analysis_results: List[Dict], save_dir: str = 'analysis'):
    """
    可视化损失函数演化过程
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    for time_id, result in enumerate(loss_analysis_results):
        training_history = result['training_history']
        
        if not training_history:
            continue
            
        # 提取数据
        epochs = [h['epoch'] for h in training_history]
        avg_losses = [h['avg_loss'] for h in training_history]
        temperatures = [h['temperature'] for h in training_history]
        
        # 任务损失和权重
        task_losses_history = np.array([h['task_losses'] for h in training_history])
        task_weights_history = np.array([h['task_weights'] for h in training_history])
        
        num_tasks = task_losses_history.shape[1]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'自适应损失函数分析 - 第{time_id+1}次运行', fontsize=16)
        
        # 1. 总损失演化
        axes[0, 0].plot(epochs, avg_losses, 'b-', linewidth=2)
        axes[0, 0].set_title('总损失演化')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Average Loss')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 温度参数演化
        axes[0, 1].plot(epochs, temperatures, 'r-', linewidth=2)
        axes[0, 1].set_title('温度参数演化')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Temperature')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 各任务损失演化
        for task_idx in range(num_tasks):
            axes[1, 0].plot(epochs, task_losses_history[:, task_idx], 
                           label=f'Task {task_idx}', linewidth=2)
        axes[1, 0].set_title('各任务损失演化')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Task Loss')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 任务权重演化
        for task_idx in range(num_tasks):
            axes[1, 1].plot(epochs, task_weights_history[:, task_idx], 
                           label=f'Task {task_idx}', linewidth=2)
        axes[1, 1].set_title('任务权重演化')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Task Weight')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{save_dir}/adaptive_loss_analysis_run_{time_id+1}.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    print(f"损失分析图表已保存到 {save_dir} 目录")


def compare_loss_functions(original_results: pd.DataFrame, adaptive_results: pd.DataFrame, 
                          metric_name: str = 'AROC', save_dir: str = 'analysis'):
    """
    比较原始损失函数和自适应损失函数的性能
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 提取测试集结果（假设最后一列是test结果）
    original_test = original_results.iloc[:, -len(original_results.columns)//3:]
    adaptive_test = adaptive_results.iloc[:, -len(adaptive_results.columns)//3:]
    
    # 移除group列
    original_test = original_test.select_dtypes(include=[np.number])
    adaptive_test = adaptive_test.select_dtypes(include=[np.number])
    
    # 创建比较图
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # 箱线图比较
    comparison_data = []
    for col in original_test.columns:
        if col in adaptive_test.columns:
            for val in original_test[col].dropna():
                comparison_data.append({'Task': col, 'Method': 'Original', 'Score': val})
            for val in adaptive_test[col].dropna():
                comparison_data.append({'Task': col, 'Method': 'Adaptive', 'Score': val})
    
    if comparison_data:
        df_comparison = pd.DataFrame(comparison_data)
        
        # 箱线图
        sns.boxplot(data=df_comparison, x='Task', y='Score', hue='Method', ax=axes[0])
        axes[0].set_title(f'{metric_name} 性能比较')
        axes[0].tick_params(axis='x', rotation=45)
        
        # 平均值比较
        mean_original = df_comparison[df_comparison['Method'] == 'Original'].groupby('Task')['Score'].mean()
        mean_adaptive = df_comparison[df_comparison['Method'] == 'Adaptive'].groupby('Task')['Score'].mean()
        
        x_pos = np.arange(len(mean_original))
        width = 0.35
        
        axes[1].bar(x_pos - width/2, mean_original.values, width, label='Original', alpha=0.8)
        axes[1].bar(x_pos + width/2, mean_adaptive.values, width, label='Adaptive', alpha=0.8)
        
        axes[1].set_title(f'{metric_name} 平均性能比较')
        axes[1].set_xlabel('Task')
        axes[1].set_ylabel(f'Average {metric_name}')
        axes[1].set_xticks(x_pos)
        axes[1].set_xticklabels(mean_original.index, rotation=45)
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/loss_function_comparison_{metric_name}.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"损失函数比较图表已保存到 {save_dir} 目录")


def generate_loss_analysis_report(loss_analysis_results: List[Dict], 
                                 dataset_analysis: Dict,
                                 save_path: str = 'analysis/adaptive_loss_report.txt'):
    """
    生成详细的损失函数分析报告
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    with open(save_path, 'w', encoding='utf-8') as f:
        f.write("自适应多任务平衡损失函数分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        # 数据集分析
        f.write("1. 数据集不平衡分析\n")
        f.write("-" * 30 + "\n")
        for task_name, stats in dataset_analysis.items():
            f.write(f"{task_name}:\n")
            f.write(f"  总样本数: {stats['total_samples']}\n")
            f.write(f"  正样本比例: {stats['positive_ratio']:.2%}\n")
            f.write(f"  不平衡比率: {stats['imbalance_ratio']:.2f}\n\n")
        
        # 损失函数性能分析
        f.write("2. 自适应损失函数性能分析\n")
        f.write("-" * 30 + "\n")
        
        if loss_analysis_results:
            # 计算平均统计
            all_final_weights = []
            all_final_difficulties = []
            
            for result in loss_analysis_results:
                final_stats = result['final_task_stats']
                all_final_weights.append(final_stats['task_weights'].cpu().numpy())
                all_final_difficulties.append(final_stats['task_difficulty'].cpu().numpy())
            
            avg_weights = np.mean(all_final_weights, axis=0)
            avg_difficulties = np.mean(all_final_difficulties, axis=0)
            
            f.write("平均任务权重分布:\n")
            for i, weight in enumerate(avg_weights):
                f.write(f"  Task {i}: {weight:.4f}\n")
            
            f.write("\n平均任务难度分布:\n")
            for i, difficulty in enumerate(avg_difficulties):
                f.write(f"  Task {i}: {difficulty:.4f}\n")
        
        f.write("\n3. 创新点总结\n")
        f.write("-" * 30 + "\n")
        f.write("• Focal Loss机制：自动关注难分类样本，缓解类别不平衡\n")
        f.write("• 动态任务权重：基于数据量和学习难度自适应调整\n")
        f.write("• 温度调节：训练过程中逐渐精细化权重分配\n")
        f.write("• 梯度平衡：确保各任务梯度贡献相对均衡\n")
    
    print(f"分析报告已保存到: {save_path}")


def create_loss_comparison_experiment(args_base: Dict) -> Dict:
    """
    创建损失函数对比实验的配置
    """
    # 基础配置
    experiment_configs = {
        'baseline': {
            'name': 'Baseline (BCE + MSE)',
            'description': '原始BCE和MSE损失函数',
            'use_adaptive': False
        },
        'adaptive_focal': {
            'name': 'Adaptive Focal Loss',
            'description': '自适应Focal Loss（仅处理类别不平衡）',
            'use_adaptive': True,
            'adaptive_params': {
                'alpha': 0.25,
                'gamma': 2.0,
                'beta': 1.0,  # 不使用动态权重
                'temperature_init': 1.0,
                'temperature_decay': 1.0  # 不衰减
            }
        },
        'adaptive_full': {
            'name': 'Full Adaptive AMTBL',
            'description': '完整自适应多任务平衡损失函数',
            'use_adaptive': True,
            'adaptive_params': {
                'alpha': 0.25,
                'gamma': 2.0,
                'beta': 0.9999,
                'temperature_init': 1.0,
                'temperature_decay': 0.995
            }
        }
    }
    
    return experiment_configs


def run_ablation_study(base_args: Dict, train_set, val_set, test_set, task_number: int):
    """
    运行消融研究，测试损失函数各组件的贡献
    """
    from utils.MY_GNN import MGA
    from torch.optim import Adam
    from utils.adaptive_loss import AdaptiveMultiTaskBalancedLoss
    
    ablation_configs = {
        'no_focal': {'gamma': 0.0, 'alpha': 0.5},  # 关闭Focal Loss
        'no_temperature': {'temperature_decay': 1.0},  # 关闭温度调节
        'no_dynamic_weight': {'beta': 1.0},  # 关闭动态权重
        'full_adaptive': {}  # 完整版本
    }
    
    results = {}
    
    for config_name, config_params in ablation_configs.items():
        print(f"\n运行消融实验: {config_name}")
        
        # 创建损失函数
        loss_params = base_args['adaptive_loss'].copy()
        loss_params.update(config_params)
        
        adaptive_loss_fn = AdaptiveMultiTaskBalancedLoss(
            classification_num=base_args['classification_num'],
            regression_num=base_args['regression_num'],
            device=base_args['device'],
            **loss_params
        )
        
        # 简化训练（仅运行几个epoch用于测试）
        model = MGA(in_feats=base_args['in_feats'], 
                   rgcn_hidden_feats=base_args['rgcn_hidden_feats'],
                   n_tasks=task_number, 
                   rgcn_drop_out=base_args['rgcn_drop_out'],
                   classifier_hidden_feats=base_args['classifier_hidden_feats'], 
                   dropout=base_args['drop_out'],
                   loop=base_args['loop'])
        
        model.to(base_args['device'])
        optimizer = Adam(model.parameters(), lr=10**-base_args['lr'])
        
        # 简单训练几个epoch
        train_loader = torch.utils.data.DataLoader(
            dataset=train_set, batch_size=base_args['batch_size'],
            shuffle=True, collate_fn=collate_molgraphs
        )
        
        epoch_losses = []
        for epoch in range(5):  # 只训练5个epoch用于快速测试
            model.train()
            batch_losses = []
            
            for batch_data in train_loader:
                smiles, bg, labels, mask = batch_data
                labels = labels.float().to(base_args['device'])
                mask = mask.float().to(base_args['device'])
                atom_feats = bg.ndata.pop(base_args['atom_data_field']).float().to(base_args['device'])
                bond_feats = bg.edata.pop(base_args['bond_data_field']).long().to(base_args['device'])
                
                logits = model(bg, atom_feats, bond_feats)
                total_loss, loss_info = adaptive_loss_fn(logits, labels, mask, epoch)
                
                optimizer.zero_grad()
                total_loss.backward()
                optimizer.step()
                
                batch_losses.append(total_loss.item())
            
            epoch_losses.append(np.mean(batch_losses))
        
        results[config_name] = {
            'final_loss': epoch_losses[-1],
            'loss_trend': epoch_losses,
            'convergence_speed': epoch_losses[0] - epoch_losses[-1]
        }
        
        print(f"  最终损失: {epoch_losses[-1]:.4f}")
        print(f"  收敛速度: {epoch_losses[0] - epoch_losses[-1]:.4f}")
    
    return results
