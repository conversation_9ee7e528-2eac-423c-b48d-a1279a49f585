"""
FC层配置相似性分析器

专门分析不同FC层数配置的MGA模型与原始3层FC配置的TFP-G参数相似性
"""

import torch
import numpy as np
import os
import sys
sys.path.append('..')
from core import ParameterExtractor, SimilarityCalculator, Visualizer
from utils.MY_GNN import MGA
import warnings
warnings.filterwarnings('ignore')


class FCLayerAnalyzer:
    """FC层配置相似性分析器"""
    
    def __init__(self, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        self.extractor = ParameterExtractor(device)
        self.calculator = SimilarityCalculator()
        self.visualizer = Visualizer('task_a_results')
        
        # 预定义FC层配置
        self.fc_configs = {
            'Layer-1': {
                'num_layers': 1,
                'hidden_dims': [128]
            },
            'Layer-2': {
                'num_layers': 2,
                'hidden_dims': [128, 128]
            },
            'Layer-4': {
                'num_layers': 4,
                'hidden_dims': [128, 128, 128, 64]
            },
            'Layer-5': {
                'num_layers': 5,
                'hidden_dims': [128, 128, 128, 64, 32]
            }
        }
    
    def create_modified_mga_model(self, base_model, base_config, fc_config):
        """创建修改后的MGA模型，复制基础模型的GNN和注意力参数"""

        class ModifiedMGA(MGA):
            def __init__(self, fc_layers_config, **kwargs):
                super().__init__(**kwargs)

                # 重新定义FC层结构
                self.fc_layers_config = fc_layers_config
                self.fc_layers1 = torch.nn.ModuleList()
                self.fc_layers2 = torch.nn.ModuleList()
                self.fc_layers3 = torch.nn.ModuleList()

                # 根据配置创建FC层
                for i in range(self.task_num):
                    layers = []
                    input_dim = self.fc_in_feats

                    # 创建指定数量的FC层
                    for layer_idx in range(fc_layers_config['num_layers']):
                        if layer_idx < len(fc_layers_config['hidden_dims']):
                            output_dim = fc_layers_config['hidden_dims'][layer_idx]
                        else:
                            output_dim = fc_layers_config['hidden_dims'][-1]

                        layers.append(self.fc_layer(kwargs.get('dropout', 0.2), input_dim, output_dim))
                        input_dim = output_dim

                    # 分配到对应的ModuleList中
                    if len(layers) >= 1:
                        self.fc_layers1.append(layers[0])
                    else:
                        self.fc_layers1.append(torch.nn.Identity())

                    if len(layers) >= 2:
                        self.fc_layers2.append(layers[1])
                    else:
                        self.fc_layers2.append(torch.nn.Identity())

                    if len(layers) >= 3:
                        self.fc_layers3.append(layers[2])
                    else:
                        self.fc_layers3.append(torch.nn.Identity())

                    # 如果有更多层，需要额外处理（这里简化为只支持最多3层）
                    if len(layers) > 3:
                        print(f"警告：当前实现只支持最多3层FC，第{i}个任务有{len(layers)}层")

            def forward(self, bg, node_feats, etype, norm=None):
                # GNN特征提取
                for gnn in self.gnn_layers:
                    node_feats = gnn(bg, node_feats, etype, norm)

                # TFP-G处理
                if self.return_weight:
                    feats_list, atom_weight_list = self.weighted_sum_readout(bg, node_feats)
                else:
                    feats_list = self.weighted_sum_readout(bg, node_feats)

                # 根据配置的层数进行前向传播
                for i in range(self.task_num):
                    mol_feats = feats_list[i]
                    current_feats = mol_feats

                    # 按顺序通过FC层
                    for layer_idx in range(self.fc_layers_config['num_layers']):
                        if layer_idx == 0 and not isinstance(self.fc_layers1[i], torch.nn.Identity):
                            current_feats = self.fc_layers1[i](current_feats)
                        elif layer_idx == 1 and not isinstance(self.fc_layers2[i], torch.nn.Identity):
                            current_feats = self.fc_layers2[i](current_feats)
                        elif layer_idx == 2 and not isinstance(self.fc_layers3[i], torch.nn.Identity):
                            current_feats = self.fc_layers3[i](current_feats)

                    predict = self.output_layer1[i](current_feats)

                    if i == 0:
                        prediction_all = predict
                    else:
                        prediction_all = torch.cat([prediction_all, predict], dim=1)

                if self.return_mol_embedding:
                    return feats_list[0]
                else:
                    if self.return_weight:
                        return prediction_all, atom_weight_list, node_feats
                    return prediction_all

        # 创建模型实例
        model = ModifiedMGA(fc_config, **base_config)
        model.to(self.device)

        # 复制基础模型的GNN和注意力参数
        self._copy_pretrained_parameters(base_model, model)

        return model

    def _copy_pretrained_parameters(self, base_model, target_model):
        """复制预训练模型的GNN和注意力参数到目标模型"""
        with torch.no_grad():
            # 复制GNN层参数
            for base_gnn, target_gnn in zip(base_model.gnn_layers, target_model.gnn_layers):
                target_gnn.load_state_dict(base_gnn.state_dict())

            # 复制注意力权重参数
            target_model.weighted_sum_readout.load_state_dict(
                base_model.weighted_sum_readout.state_dict()
            )

            # 复制输出层参数（保持一致）
            for i in range(min(len(base_model.output_layer1), len(target_model.output_layer1))):
                target_model.output_layer1[i].load_state_dict(
                    base_model.output_layer1[i].state_dict()
                )

    def _initialize_new_fc_layers(self, model, fc_config):
        """对新的FC层进行合理初始化"""
        with torch.no_grad():
            for i in range(model.task_num):
                # 初始化FC层1
                if (hasattr(model, 'fc_layers1') and len(model.fc_layers1) > i and
                    not isinstance(model.fc_layers1[i], torch.nn.Identity)):
                    for module in model.fc_layers1[i].modules():
                        if isinstance(module, torch.nn.Linear):
                            torch.nn.init.xavier_uniform_(module.weight)
                            if module.bias is not None:
                                torch.nn.init.zeros_(module.bias)

                # 初始化FC层2
                if (hasattr(model, 'fc_layers2') and len(model.fc_layers2) > i and
                    not isinstance(model.fc_layers2[i], torch.nn.Identity)):
                    for module in model.fc_layers2[i].modules():
                        if isinstance(module, torch.nn.Linear):
                            torch.nn.init.xavier_uniform_(module.weight)
                            if module.bias is not None:
                                torch.nn.init.zeros_(module.bias)

                # 初始化FC层3
                if (hasattr(model, 'fc_layers3') and len(model.fc_layers3) > i and
                    not isinstance(model.fc_layers3[i], torch.nn.Identity)):
                    for module in model.fc_layers3[i].modules():
                        if isinstance(module, torch.nn.Linear):
                            torch.nn.init.xavier_uniform_(module.weight)
                            if module.bias is not None:
                                torch.nn.init.zeros_(module.bias)
    
    def analyze_fc_layer_similarity(self, base_model_path, base_config):
        """
        分析不同FC层配置的相似性

        Args:
            base_model_path (str): 基础模型路径
            base_config (dict): 基础模型配置

        Returns:
            tuple: (相似性矩阵, 模型名称列表)
        """
        print("开始任务A：FC层配置相似性分析")
        print("-" * 50)

        # 加载基础模型（3层FC）
        if not os.path.exists(base_model_path):
            raise FileNotFoundError(f"基础模型文件不存在: {base_model_path}")

        base_model = self.extractor.load_model_from_checkpoint(base_model_path, base_config)
        base_params = self.extractor.extract_tfp_parameters(base_model, 'Layer-3')
        print(f"已加载基础模型: {base_model_path}")

        # 创建不同FC层配置的模型
        all_params = {'Layer-3': base_params}

        for config_name, fc_config in self.fc_configs.items():
            print(f"创建 {config_name} 配置模型...")
            model = self.create_modified_mga_model(base_model, base_config, fc_config)

            # 对新的FC层进行合理初始化（使用Xavier初始化）
            self._initialize_new_fc_layers(model, fc_config)

            params = self.extractor.extract_tfp_parameters(model, config_name)
            all_params[config_name] = params
        
        # 计算相似性矩阵
        similarity_matrix, model_names = self.calculator.compute_similarity_matrix(all_params)
        
        # 生成热力图
        self.visualizer.create_heatmap(
            similarity_matrix,
            model_names,
            'TFP-G Parameter Similarity: Different FC Layer Configurations',
            'fc_layer_similarity_heatmap.png',
            task_type='task_a'  # 指定为任务A，字体放大两号
        )
        
        # 保存相似性矩阵
        self.visualizer.save_similarity_matrix(
            similarity_matrix, 
            model_names, 
            'fc_layer_similarity_matrix.csv'
        )
        
        # 计算统计信息
        stats = self.calculator.get_similarity_statistics(similarity_matrix)
        similarities_list = self.calculator.compute_pairwise_similarities(all_params)
        
        # 生成分析报告
        self.visualizer.generate_analysis_report(
            similarity_matrix, 
            model_names, 
            similarities_list, 
            stats,
            'fc_layer_analysis_report.txt',
            "任务A：FC层配置相似性分析"
        )
        
        # 打印结果
        print("\nFC层配置相似性结果:")
        for i, name1 in enumerate(model_names):
            for j, name2 in enumerate(model_names):
                if i < j:
                    print(f"{name1} vs {name2}: {similarity_matrix[i, j]:.4f}")
        
        print(f"\n任务A分析完成！结果保存在 task_a_results/ 目录")
        return similarity_matrix, model_names
    
    def get_default_base_config(self):
        """获取默认的基础模型配置"""
        return {
            'in_feats': 40,
            'rgcn_hidden_feats': [128, 128],
            'n_tasks': 5,  # MTL-scr有5个任务
            'classifier_hidden_feats': 128,
            'rgcn_drop_out': 0.4,
            'dropout': 0.3,
            'loop': True,
            'return_mol_embedding': False,
            'return_weight': False
        }
