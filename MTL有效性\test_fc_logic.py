#!/usr/bin/env python3
"""
测试FC参数处理逻辑
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

def test_current_logic():
    """测试当前的处理逻辑"""
    print("=" * 60)
    print("测试当前FC参数处理逻辑")
    print("=" * 60)
    
    # 模拟从模型中提取的FC参数（字典格式）
    fc_params = {
        'FishLC50': {
            'fc1_task_0_weight': np.random.normal(0.1, 0.05, 128),
            'fc1_task_0_bias': np.random.normal(0.0, 0.01, 64),
            'output_task_0_weight': np.random.normal(0.0, 0.02, 64),
            'output_task_0_bias': np.array([0.1])
        },
        'FishEL_NOEC': {
            'fc1_task_0_weight': np.random.normal(0.12, 0.06, 64),
            'fc1_task_0_bias': np.random.normal(0.0, 0.01, 32),
            'output_task_0_weight': np.random.normal(0.0, 0.02, 32),
            'output_task_0_bias': np.array([0.05])
        },
        'DMRepNOEC': {
            'fc1_task_0_weight': np.random.normal(-0.05, 0.08, 64),
            'fc1_task_0_bias': np.random.normal(0.0, 0.01, 32),
            'output_task_0_weight': np.random.normal(0.0, 0.02, 32),
            'output_task_0_bias': np.array([-0.02])
        },
        'DMImbEC50': {
            'fc1_task_0_weight': np.random.normal(-0.04, 0.11, 64),
            'fc1_task_0_bias': np.random.normal(0.0, 0.01, 32),
            'output_task_0_weight': np.random.normal(0.0, 0.02, 32),
            'output_task_0_bias': np.array([-0.01])
        },
        'AlaGroErC50': {
            'fc1_task_0_weight': np.random.normal(0.2, 0.1, 256),
            'fc1_task_0_bias': np.random.normal(0.0, 0.01, 128),
            'output_task_0_weight': np.random.normal(0.0, 0.02, 128),
            'output_task_0_bias': np.array([0.15])
        }
    }
    
    print("模拟的FC参数:")
    for task_name, params in fc_params.items():
        total_params = sum(len(param) for param in params.values())
        print(f"{task_name}: {len(params)}个参数组, 总维度{total_params}")
    
    # 应用当前的处理逻辑
    print(f"\n应用当前处理逻辑:")
    processed_fc_params = {}
    fc_dimensions = []
    
    for task_name, params in fc_params.items():
        if isinstance(params, dict) and params:
            param_vector = np.concatenate([param for param in params.values()])
        else:
            param_vector = np.array([0.0])
        
        processed_fc_params[task_name] = param_vector
        fc_dimensions.append(len(param_vector))
        print(f"{task_name}: {len(param_vector)}维")
    
    # 检查是否需要截断
    print(f"\n维度检查:")
    print(f"所有维度: {fc_dimensions}")
    print(f"唯一维度: {set(fc_dimensions)}")
    
    if len(set(fc_dimensions)) > 1:
        print("维度不一致，应用截断...")
        min_dim = min(fc_dimensions)
        print(f"截断到最小维度: {min_dim}")
        
        filtered_fc_params = {}
        for task_name, param_vector in processed_fc_params.items():
            if len(param_vector) > min_dim:
                filtered_fc_params[task_name] = param_vector[:min_dim]
                print(f"{task_name}: 截断到{min_dim}维")
            else:
                filtered_fc_params[task_name] = param_vector
                print(f"{task_name}: 保持{len(param_vector)}维")
    else:
        print("所有维度相同，无需截断")
        filtered_fc_params = processed_fc_params
    
    # 计算相似性
    print(f"\n计算相似性:")
    params_list = []
    task_names = []
    for task_name, param_vector in filtered_fc_params.items():
        params_list.append(param_vector)
        task_names.append(task_name)
        print(f"{task_name}: 最终维度{len(param_vector)}, 均值{np.mean(param_vector):.4f}")
    
    similarity_matrix = cosine_similarity(params_list)
    
    # 显示名称映射
    display_mapping = {
        'FishLC50': 'FishAT',
        'FishEL_NOEC': 'FishCT', 
        'DMRepNOEC': 'DMCT',
        'DMImbEC50': 'DMAT',
        'AlaGroErC50': 'AlgAT'
    }
    
    display_names = [display_mapping.get(name, name) for name in task_names]
    
    print(f"\n相似性矩阵:")
    print("        ", "  ".join(f"{name:>8}" for name in display_names))
    for i, name in enumerate(display_names):
        row = "  ".join(f"{similarity_matrix[i][j]:8.4f}" for j in range(len(display_names)))
        print(f"{name:>8}: {row}")
    
    # 分析结果
    fish_at_idx = task_names.index('FishLC50')
    fish_ct_idx = task_names.index('FishEL_NOEC')
    dm_ct_idx = task_names.index('DMRepNOEC')
    
    fish_at_ct_sim = similarity_matrix[fish_at_idx][fish_ct_idx]
    fish_ct_dm_ct_sim = similarity_matrix[fish_ct_idx][dm_ct_idx]
    
    print(f"\n关键相似性:")
    print(f"FishAT vs FishCT: {fish_at_ct_sim:.4f}")
    print(f"FishCT vs DMCT: {fish_ct_dm_ct_sim:.4f}")
    
    if fish_at_ct_sim > fish_ct_dm_ct_sim:
        print("✅ 结果合理：鱼类间相似性 > 跨物种相似性")
    else:
        print("❌ 结果不合理：鱼类间相似性 < 跨物种相似性")
    
    return similarity_matrix

def main():
    """主函数"""
    print("测试FC参数处理逻辑...")
    test_current_logic()
    
    print(f"\n" + "=" * 60)
    print("如果测试结果合理，说明逻辑没问题")
    print("如果实际结果不合理，可能是:")
    print("1. FC参数提取有问题")
    print("2. 某些模型的FC参数为空")
    print("3. 模型加载或配置有问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
