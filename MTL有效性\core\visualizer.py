"""
可视化工具模块

提供热力图生成和分析报告功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from matplotlib.patches import Rectangle
import warnings
import sys
warnings.filterwarnings('ignore')

# 导入可视化配置
sys.path.append('.')
try:
    from visualization_config import get_figure_size, get_heatmap_style, apply_font_config
except ImportError:
    # 如果配置文件不存在，使用默认值
    def get_figure_size(size_type='heatmap'):
        return (12, 10)
    def get_heatmap_style():
        return {'annot': True, 'fmt': '.3f', 'cmap': 'Blues', 'linewidths': 0}
    def apply_font_config():
        pass


class Visualizer:
    """可视化工具"""
    
    def __init__(self, output_dir='analysis_results'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置Times New Roman字体
        plt.rcParams['font.family'] = 'serif'
        plt.rcParams['font.serif'] = 'Times New Roman'
        plt.rcParams['mathtext.fontset'] = 'stix'
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['figure.dpi'] = 300
    
    def create_heatmap(self, similarity_matrix, labels, title, filename,
                      figsize=None, cmap='Blues', add_boxes=False,
                      group_info=None, task_type='task_b'):
        """
        创建相似性热力图

        Args:
            similarity_matrix (np.ndarray): 相似性矩阵
            labels (list): 标签列表
            title (str): 图标题
            filename (str): 保存文件名
            figsize (tuple): 图片大小，如果为None则使用默认统一尺寸
            cmap (str): 颜色映射，默认使用蓝色渐变（高相似性深蓝，低相似性浅蓝）
            add_boxes (bool): 是否添加分组框
            group_info (dict): 分组信息
            task_type (str): 任务类型，'task_a' 或 'task_b'，用于调整字体大小

        Returns:
            tuple: (fig, ax)
        """
        # 使用统一的默认尺寸
        if figsize is None:
            figsize = (12, 10)  # 统一的默认尺寸

        # 根据任务类型设置字体大小（任务A放大一号）
        if task_type == 'task_a':
            # 任务A：所有字体放大一号
            annot_fontsize = 17  # 数值标注字体 (原16 + 1)
            tick_fontsize = 21   # 轴标签字体 (原20 + 1)
            cbar_fontsize = 23   # 颜色条字体 (原22 + 1)
            cbar_tick_fontsize = 21  # 颜色条刻度字体 (原20 + 1)
            title_fontsize = 25  # 标题字体 (原24 + 1)
        else:
            # 任务B：保持原有字体大小
            annot_fontsize = 16
            tick_fontsize = 20
            cbar_fontsize = 22
            cbar_tick_fontsize = 20
            title_fontsize = 24

        fig, ax = plt.subplots(figsize=figsize)

        # 使用seaborn创建无网格线的热力图
        sns.heatmap(similarity_matrix,
                   annot=True,  # 显示数值
                   fmt='.3f',   # 数值格式
                   cmap=cmap,   # 颜色映射
                   vmin=0, vmax=1,  # 值范围
                   xticklabels=labels,  # x轴标签
                   yticklabels=labels,  # y轴标签
                   square=True,  # 正方形单元格
                   linewidths=0,  # 无网格线
                   cbar_kws={'shrink': 1.0, 'aspect': 20, 'label': 'Similarity'},  # 颜色条设置
                   annot_kws={'fontsize': annot_fontsize, 'fontweight': 'bold'},  # 数值标注设置
                   ax=ax)

        # 设置标签字体
        ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right',
                          fontsize=tick_fontsize, family='Times New Roman')
        ax.set_yticklabels(ax.get_yticklabels(), rotation=0,
                          fontsize=tick_fontsize, family='Times New Roman')

        # 设置颜色条标签字体
        cbar = ax.collections[0].colorbar
        cbar.set_label('Similarity', rotation=270, labelpad=20,
                      fontsize=cbar_fontsize, family='Times New Roman')
        cbar.ax.tick_params(labelsize=cbar_tick_fontsize)

        # 添加分组框（如果提供了分组信息）
        if add_boxes and group_info:
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
            for idx, (group_name, indices) in enumerate(group_info.items()):
                if len(indices) > 1:
                    min_idx, max_idx = min(indices), max(indices)
                    rect = Rectangle((min_idx-0.5, min_idx-0.5),
                                   max_idx-min_idx+1, max_idx-min_idx+1,
                                   linewidth=2, edgecolor=colors[idx % len(colors)],
                                   facecolor='none', linestyle='--', alpha=0.8)
                    ax.add_patch(rect)
        
        # 设置标题
        ax.set_title(title, fontsize=title_fontsize, fontweight='bold', pad=20, family='Times New Roman')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        output_path = os.path.join(self.output_dir, filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        
        print(f"热力图已保存: {output_path}")
        return fig, ax
    
    def save_similarity_matrix(self, similarity_matrix, labels, filename):
        """
        保存相似性矩阵到CSV文件
        
        Args:
            similarity_matrix (np.ndarray): 相似性矩阵
            labels (list): 标签列表
            filename (str): 文件名
        """
        similarity_df = pd.DataFrame(similarity_matrix, 
                                   index=labels, 
                                   columns=labels)
        output_path = os.path.join(self.output_dir, filename)
        similarity_df.to_csv(output_path)
        print(f"相似性矩阵已保存: {output_path}")
    
    def generate_analysis_report(self, similarity_matrix, labels, similarities_list, 
                               stats, filename, task_name="TFP-G参数相似性分析"):
        """
        生成分析报告
        
        Args:
            similarity_matrix (np.ndarray): 相似性矩阵
            labels (list): 标签列表
            similarities_list (list): 相似性对列表
            stats (dict): 统计信息
            filename (str): 报告文件名
            task_name (str): 任务名称
        """
        report_path = os.path.join(self.output_dir, filename)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"{task_name}报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 基本统计
            f.write(f"相似性统计:\n")
            f.write(f"  平均相似性: {stats['mean']:.4f}\n")
            f.write(f"  标准差: {stats['std']:.4f}\n")
            f.write(f"  最大相似性: {stats['max']:.4f}\n")
            f.write(f"  最小相似性: {stats['min']:.4f}\n")
            f.write(f"  中位数: {stats['median']:.4f}\n\n")
            
            # 最相似的任务对
            f.write("最相似的任务对 (Top 10):\n")
            similarities_sorted = sorted(similarities_list, key=lambda x: x[2], reverse=True)
            for i, (task1, task2, sim) in enumerate(similarities_sorted[:10]):
                f.write(f"  {i+1}. {task1} vs {task2}: {sim:.4f}\n")
            
            f.write("\n最不相似的任务对 (Bottom 10):\n")
            for i, (task1, task2, sim) in enumerate(similarities_sorted[-10:]):
                f.write(f"  {i+1}. {task1} vs {task2}: {sim:.4f}\n")
        
        print(f"分析报告已保存: {report_path}")
    
    def create_comparison_plot(self, data_dict, title, filename, figsize=(12, 8)):
        """
        创建对比图
        
        Args:
            data_dict (dict): 数据字典
            title (str): 图标题
            filename (str): 文件名
            figsize (tuple): 图片大小
        """
        fig, ax = plt.subplots(figsize=figsize)
        
        # 创建条形图
        names = list(data_dict.keys())
        values = list(data_dict.values())
        
        bars = ax.bar(names, values, color='skyblue', alpha=0.7)
        
        # 添加数值标注
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=18)
        
        ax.set_title(title, fontsize=22, fontweight='bold')
        ax.set_ylabel('Similarity', fontsize=20)
        plt.xticks(rotation=45, ha='right', fontsize=18)
        plt.yticks(fontsize=18)
        plt.tight_layout()
        
        # 保存图片
        output_path = os.path.join(self.output_dir, filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"对比图已保存: {output_path}")
        return fig, ax
