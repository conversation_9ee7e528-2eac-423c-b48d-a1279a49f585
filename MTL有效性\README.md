# MTL有效性分析工具

本工具用于分析MGA模型中TFP-G（毒性指纹发生器）组件的参数相似性，实现两个核心分析任务。

## 🎯 核心功能

### 任务A：FC层配置相似性分析
- **目标**：分析不同FC层数配置的MGA模型与原始3层FC配置的TFP-G参数相似性
- **对比配置**：1层、2层、4层、5层FC配置与基准3层FC模型
- **验证内容**：FC层数变化对模型参数学习的影响

### 任务B：毒性任务间相似性分析
- **目标**：分析不同毒性预测任务之间的TFP-G参数相似性
- **多任务模型**：MTL-scr (5个任务)
- **单任务模型**：FishLC50、FishEL_NOEC、DMRepNOEC、DMImbEC50、AlaGroErC50
- **验证内容**：多任务学习与单任务学习在参数共享方面的差异

## 📁 项目结构

```
code/MTL有效性/
├── core/                           # 核心共享模块
│   ├── __init__.py
│   ├── parameter_extractor.py      # 参数提取器
│   ├── similarity_calculator.py    # 相似性计算器
│   └── visualizer.py              # 可视化工具
├── task_a/                        # 任务A：FC层配置相似性分析
│   ├── __init__.py
│   ├── fc_layer_analyzer.py       # FC层分析器
│   └── run_task_a.py             # 任务A执行脚本
├── task_b/                        # 任务B：毒性任务间相似性分析
│   ├── __init__.py
│   ├── toxicity_task_analyzer.py  # 毒性任务分析器
│   └── run_task_b.py             # 任务B执行脚本
├── README.md                      # 项目文档
└── run_all_analysis.py           # 主执行脚本
```

## 🚀 快速开始

### 环境要求
- Python 3.7+
- PyTorch
- NumPy, Pandas, Matplotlib, Seaborn
- scikit-learn

### 运行方式

#### 🎯 推荐方式：菜单系统（功能完全分离）
```bash
cd code/MTL有效性
python run_analysis_menu.py
```
提供完整的交互式菜单，包含任务信息、结果说明等

#### 🚀 直接运行（无交互）
```bash
# 直接运行任务A
python run_task_a_direct.py

# 直接运行任务B
python run_task_b_direct.py


#### 📋 传统方式
```bash
# 交互式选择
python run_all_analysis.py

# 命令行参数
python run_all_analysis.py --task a    # 任务A
python run_all_analysis.py --task b    # 任务B
python run_all_analysis.py --task all  # 所有任务
```

#### 🔍 模块内运行
```bash
# 在模块目录内运行
cd task_a && python run_task_a.py
cd task_b && python run_task_b.py
```

## 📊 输出结果

### 任务A输出文件
- `task_a_results/fc_layer_similarity_heatmap.png` - FC层配置相似性热力图
- `task_a_results/fc_layer_similarity_matrix.csv` - 相似性数值矩阵
- `task_a_results/fc_layer_analysis_report.txt` - 详细分析报告

### 任务B输出文件
- `task_b_results/overall_task_similarity_heatmap.png` - 整体任务相似性热力图
- `task_b_results/attention_similarity_heatmap.png` - 注意力权重相似性热力图
- `task_b_results/fc_similarity_heatmap.png` - FC层参数相似性热力图
- `task_b_results/overall_task_similarity_matrix.csv` - 整体相似性矩阵
- `task_b_results/attention_similarity_matrix.csv` - 注意力相似性矩阵
- `task_b_results/fc_similarity_matrix.csv` - FC层相似性矩阵
- `task_b_results/overall_task_analysis_report.txt` - 详细分析报告

## 🔧 核心组件

### 参数提取器 (ParameterExtractor)
- 从MGA模型中提取TFP-G相关参数
- 支持注意力权重和全连接层参数提取
- 兼容不同模型配置

### 相似性计算器 (SimilarityCalculator)
- 支持多种相似性度量方法：
  - 余弦相似性（默认）
  - 皮尔逊相关系数
  - 欧几里得距离
- 提供统计分析功能

### 可视化工具 (Visualizer)
- 生成高质量热力图
- 自动保存分析报告
- 支持自定义样式和分组

## 📋 模型配置

### 多任务模型配置 (MTL-scr)
```python
mtl_config = {
    'in_feats': 40,
    'rgcn_hidden_feats': [128, 128],
    'n_tasks': 5,  # 多任务模型
    'classifier_hidden_feats': 128,
    'rgcn_drop_out': 0.4,
    'dropout': 0.3,
    'loop': True,
    'return_mol_embedding': False,
    'return_weight': False
}
```

### 单任务模型配置
```python
stl_config = {
    'in_feats': 40,
    'rgcn_hidden_feats': [128, 128],
    'n_tasks': 1,  # 单任务模型
    'classifier_hidden_feats': 128,
    'rgcn_drop_out': 0.2,
    'dropout': 0.2,
    'loop': True,
    'return_mol_embedding': False,
    'return_weight': False
}
```

### FC层配置变体
- **1层FC**: [128]
- **2层FC**: [128, 128]
- **3层FC**: [128, 128, 128] (基准)
- **4层FC**: [128, 128, 128, 64]
- **5层FC**: [128, 128, 128, 64, 32]

## ⚠️ 注意事项

1. **模型文件路径**：确保模型文件存在于指定路径
2. **GPU内存**：大型模型可能需要足够的GPU内存
3. **参数匹配**：不同任务的模型参数结构需要兼容
4. **结果解释**：相似性值范围[0,1]，1表示完全相似，0表示完全不同

## 🔍 分析方法

### TFP-G组件结构
- **深度毒性提取器(DTE)**: RGCN层提取分子特征
- **注意力机制**: WeightAndSum类中的atom_weighting_specific
- **全连接层**: fc_layers1, fc_layers2, fc_layers3
- **输出层**: output_layer1

### 相似性计算
1. 提取模型TFP-G相关参数
2. 将参数展平为向量
3. 计算向量间相似性
4. 生成相似性矩阵和可视化

## 📈 扩展功能

- 支持自定义相似性度量方法
- 可添加新的参数类型分析
- 支持批量模型对比
- 提供详细的统计分析报告

## 🤝 使用建议

1. 首次使用建议运行完整分析了解工具功能
2. 根据研究需求选择特定任务运行
3. 查看生成的报告文件获取详细分析结果
4. 可根据需要调整模型配置和分析参数
